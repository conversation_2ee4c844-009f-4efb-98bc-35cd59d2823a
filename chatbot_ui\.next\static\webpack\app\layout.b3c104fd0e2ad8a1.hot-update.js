"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0f1786727233\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcY2hhdGJvdCBmZSBuIGJlXFxjaGF0Ym90X3VpXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZjE3ODY3MjcyMzNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ChatWidget.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWidget.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,Loader2,RefreshCw,RotateCcw,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AVATAR_URL = \"/images/operator1.png\";\nconst ChatWidget = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBotTyping, setIsBotTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitializing, setIsInitializing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetting, setIsResetting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetConfirm, setShowResetConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Always set to true - no more terms and conditions\n    const hasConsented = true;\n    const [showScrollButton, setShowScrollButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [widgetAnimation, setWidgetAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [buttonAnimation, setButtonAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [indicatorAnimation, setIndicatorAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const hasInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const pendingMessageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Check for mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            const checkIsMobile = {\n                \"ChatWidget.useEffect.checkIsMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatWidget.useEffect.checkIsMobile\"];\n            // Check initially\n            checkIsMobile();\n            // Add resize listener\n            window.addEventListener(\"resize\", checkIsMobile);\n            // Clean up\n            return ({\n                \"ChatWidget.useEffect\": ()=>window.removeEventListener(\"resize\", checkIsMobile)\n            })[\"ChatWidget.useEffect\"];\n        }\n    }[\"ChatWidget.useEffect\"], []);\n    // Get or create chatId from localStorage - now with browser check\n    const [chatId, setChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ChatWidget.useState\": ()=>{\n            // Default value in case we're on the server\n            const defaultId = (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n            // Only access localStorage in the browser\n            if (true) {\n                const savedChatId = localStorage.getItem(\"chat-widget-id\");\n                console.log(\"Retrieved chat ID from localStorage:\", savedChatId);\n                if (savedChatId) {\n                    // Validate that it's a proper UUID\n                    try {\n                        // Simple UUID validation\n                        if (savedChatId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {\n                            console.log(\"Using existing chat ID:\", savedChatId);\n                            return savedChatId;\n                        } else {\n                            console.log(\"Invalid chat ID format, creating new one\");\n                        }\n                    } catch (e) {\n                        console.log(\"Error validating chat ID, creating new one\");\n                    }\n                }\n                // Create new chat ID and save it\n                console.log(\"Creating new chat ID:\", defaultId);\n                localStorage.setItem(\"chat-widget-id\", defaultId);\n                return defaultId;\n            }\n            return defaultId;\n        }\n    }[\"ChatWidget.useState\"]);\n    // Consent is now always true by default\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            // Set consent in localStorage for consistency\n            if (true) {\n                localStorage.setItem(\"chat-consent\", \"true\");\n            }\n        }\n    }[\"ChatWidget.useEffect\"], []);\n    // Socket connection handling - now independent of isOpen state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            // Only create the connection if it doesn't exist yet and we're in the browser\n            if (socketRef.current || \"object\" === \"undefined\") return;\n            const BACKEND_URL = \"http://localhost:3001\" || 0;\n            console.log(\"Connecting to backend:\", BACKEND_URL);\n            console.log(\"Using chat ID:\", chatId);\n            socketRef.current = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(BACKEND_URL, {\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                timeout: 20000,\n                reconnection: true,\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000\n            });\n            const socket = socketRef.current;\n            socket.on(\"connect\", {\n                \"ChatWidget.useEffect\": ()=>{\n                    console.log(\"Socket connected successfully\");\n                    setIsConnected(true);\n                    if (!hasInitializedRef.current) {\n                        console.log(\"Joining chat with ID:\", chatId);\n                        socket.emit(\"join-chat\", {\n                            chatId\n                        });\n                        // No need to check terms status anymore\n                        socket.emit(\"accept-terms\", {\n                            chatId\n                        }); // Auto-accept terms\n                        hasInitializedRef.current = true;\n                    }\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"disconnect\", {\n                \"ChatWidget.useEffect\": (reason)=>{\n                    console.log(\"Socket disconnected:\", reason);\n                    setIsConnected(false);\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"connect_error\", {\n                \"ChatWidget.useEffect\": (error)=>{\n                    console.error(\"Socket connection error:\", error);\n                    setIsConnected(false);\n                }\n            }[\"ChatWidget.useEffect\"]);\n            // We still listen for terms-status for backward compatibility\n            socket.on(\"terms-status\", {\n                \"ChatWidget.useEffect\": ()=>{\n                // Terms are always accepted now\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"chat-history\", {\n                \"ChatWidget.useEffect\": (history)=>{\n                    console.log(\"Received chat history:\", history);\n                    if (messagesRef.current.length === 0) {\n                        console.log(\"Setting initial chat history with\", history.length, \"messages\");\n                        setMessages(history);\n                        messagesRef.current = history;\n                        // Mark that we should restore scroll position after component updates\n                        if (isOpen) {\n                            shouldRestoreScrollRef.current = true;\n                        }\n                    } else {\n                        console.log(\"Ignoring chat history - already have messages\");\n                    }\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"new-messages\", {\n                \"ChatWidget.useEffect\": (newMessages)=>{\n                    console.log(\"Received new messages:\", newMessages);\n                    setIsBotTyping(false);\n                    setMessages({\n                        \"ChatWidget.useEffect\": (prevMessages)=>{\n                            console.log(\"Current messages count:\", prevMessages.length);\n                            const existingIds = new Set(prevMessages.map({\n                                \"ChatWidget.useEffect\": (msg)=>msg.id\n                            }[\"ChatWidget.useEffect\"]));\n                            const filteredMessages = newMessages.filter({\n                                \"ChatWidget.useEffect.filteredMessages\": (msg)=>!existingIds.has(msg.id) && !(msg.role === \"user\" && msg.content === pendingMessageRef.current)\n                            }[\"ChatWidget.useEffect.filteredMessages\"]).map({\n                                \"ChatWidget.useEffect.filteredMessages\": (msg)=>({\n                                        ...msg,\n                                        content: msg.content.replace(/<think>.*?<\\/think>/g, \"\").trim()\n                                    })\n                            }[\"ChatWidget.useEffect.filteredMessages\"]);\n                            console.log(\"Filtered messages to add:\", filteredMessages);\n                            const updatedMessages = [\n                                ...prevMessages,\n                                ...filteredMessages\n                            ];\n                            messagesRef.current = updatedMessages;\n                            console.log(\"Updated messages count:\", updatedMessages.length);\n                            if (isOpen && isNearBottom()) {\n                                setTimeout(scrollToBottom, 100);\n                            }\n                            return updatedMessages;\n                        }\n                    }[\"ChatWidget.useEffect\"]);\n                    // Clear pending message after processing\n                    pendingMessageRef.current = \"\";\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"error\", {\n                \"ChatWidget.useEffect\": (error)=>{\n                    // Handle API errors\n                    setIsBotTyping(false);\n                    // Service error handling simplified - no need to set flag anymore\n                    // Create a system message to display the error\n                    const errorMessage = {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n                        content: error.code === \"SERVICE_UNAVAILABLE\" ? \"Maaf, layanan AI sedang sibuk. Mohon coba lagi setelah beberapa saat.\" : \"Terjadi kesalahan dalam memproses pesan Anda. Mohon coba lagi nanti.\",\n                        timestamp: new Date().toISOString(),\n                        role: \"assistant\",\n                        status: \"error\"\n                    };\n                    setMessages({\n                        \"ChatWidget.useEffect\": (prev)=>{\n                            const updatedMessages = [\n                                ...prev,\n                                errorMessage\n                            ];\n                            messagesRef.current = updatedMessages;\n                            return updatedMessages;\n                        }\n                    }[\"ChatWidget.useEffect\"]);\n                    if (isOpen && isNearBottom()) {\n                        setTimeout(scrollToBottom, 100);\n                    }\n                }\n            }[\"ChatWidget.useEffect\"]);\n            socket.on(\"chat-created\", {\n                \"ChatWidget.useEffect\": (newChatId)=>{\n                    console.log(\"Received new chat ID from server:\", newChatId);\n                    if (true) {\n                        localStorage.setItem(\"chat-widget-id\", newChatId);\n                        console.log(\"Saved new chat ID to localStorage:\", newChatId);\n                    }\n                    setChatId(newChatId);\n                }\n            }[\"ChatWidget.useEffect\"]);\n            // Listen for chat reset event\n            socket.on(\"chat-reset\", {\n                \"ChatWidget.useEffect\": (data)=>{\n                    console.log(\"Chat reset received:\", data);\n                    if (true) {\n                        localStorage.setItem(\"chat-widget-id\", data.newChatId);\n                        console.log(\"Updated chat ID in localStorage:\", data.newChatId);\n                    }\n                    setChatId(data.newChatId);\n                    setIsResetting(false);\n                    setMessages([]);\n                    // Clear any pending message\n                    pendingMessageRef.current = \"\";\n                }\n            }[\"ChatWidget.useEffect\"]);\n            // Cleanup function - now only disconnects when component unmounts\n            return ({\n                \"ChatWidget.useEffect\": ()=>{\n                    if (chatContainerRef.current) {\n                        scrollPositionRef.current = chatContainerRef.current.scrollTop;\n                    }\n                    socket.disconnect();\n                    socketRef.current = null;\n                    hasInitializedRef.current = false;\n                }\n            })[\"ChatWidget.useEffect\"];\n        }\n    }[\"ChatWidget.useEffect\"], [\n        chatId\n    ]); // No longer dependent on isOpen\n    // Handle opening and closing of chat widget\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && socketRef.current) {\n                // When widget opens, fetch latest chat history if needed\n                socketRef.current.emit(\"get-chat-history\", {\n                    chatId\n                });\n                // Mark that we should restore scroll position\n                shouldRestoreScrollRef.current = true;\n                // For mobile devices, prevent body scrolling when chat is open\n                if (isMobile && typeof document !== \"undefined\") {\n                    document.body.style.overflow = \"hidden\";\n                }\n            } else if (isMobile && typeof document !== \"undefined\") {\n                // Re-enable body scrolling when chat is closed\n                document.body.style.overflow = \"\";\n            }\n            return ({\n                \"ChatWidget.useEffect\": ()=>{\n                    // Clean up - ensure body scrolling is re-enabled\n                    if (isMobile && typeof document !== \"undefined\") {\n                        document.body.style.overflow = \"\";\n                    }\n                }\n            })[\"ChatWidget.useEffect\"];\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        chatId,\n        isMobile\n    ]);\n    // Effect to restore scroll position when chat reopens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && shouldRestoreScrollRef.current && chatContainerRef.current) {\n                // Use a short timeout to ensure the DOM has updated\n                const timer = setTimeout({\n                    \"ChatWidget.useEffect.timer\": ()=>{\n                        if (chatContainerRef.current) {\n                            // If we have a saved position, restore to that position\n                            if (scrollPositionRef.current > 0) {\n                                chatContainerRef.current.scrollTop = scrollPositionRef.current;\n                            }\n                            shouldRestoreScrollRef.current = false;\n                        }\n                    }\n                }[\"ChatWidget.useEffect.timer\"], 100);\n                return ({\n                    \"ChatWidget.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChatWidget.useEffect\"];\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen,\n        messages\n    ]);\n    // Scroll button visibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            const container = chatContainerRef.current;\n            if (!container || !hasConsented || !isOpen) return;\n            const handleScroll = {\n                \"ChatWidget.useEffect.handleScroll\": ()=>{\n                    const distanceFromBottom = container.scrollHeight - container.scrollTop - container.clientHeight;\n                    setShowScrollButton(distanceFromBottom > 150);\n                }\n            }[\"ChatWidget.useEffect.handleScroll\"];\n            container.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"ChatWidget.useEffect\": ()=>container.removeEventListener(\"scroll\", handleScroll)\n            })[\"ChatWidget.useEffect\"];\n        }\n    }[\"ChatWidget.useEffect\"], [\n        hasConsented,\n        messages,\n        isOpen\n    ]);\n    // Auto-scroll when bot is typing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isBotTyping && isOpen && isNearBottom()) {\n                scrollToBottom();\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isBotTyping,\n        isOpen\n    ]);\n    // Save scroll position periodically\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            if (isOpen && chatContainerRef.current) {\n                const saveScrollPosition = {\n                    \"ChatWidget.useEffect.saveScrollPosition\": ()=>{\n                        if (chatContainerRef.current) {\n                            scrollPositionRef.current = chatContainerRef.current.scrollTop;\n                        }\n                    }\n                }[\"ChatWidget.useEffect.saveScrollPosition\"];\n                // Save position on scroll\n                chatContainerRef.current.addEventListener(\"scroll\", saveScrollPosition);\n                return ({\n                    \"ChatWidget.useEffect\": ()=>{\n                        if (chatContainerRef.current) {\n                            chatContainerRef.current.removeEventListener(\"scroll\", saveScrollPosition);\n                        }\n                    }\n                })[\"ChatWidget.useEffect\"];\n            }\n        }\n    }[\"ChatWidget.useEffect\"], [\n        isOpen\n    ]);\n    // Helper functions\n    const isNearBottom = ()=>{\n        const container = chatContainerRef.current;\n        if (!container) return true;\n        return container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n        setShowScrollButton(false);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const toggleChat = ()=>{\n        if (isOpen) {\n            // Closing the chat - save current scroll position\n            if (chatContainerRef.current) {\n                scrollPositionRef.current = chatContainerRef.current.scrollTop;\n            }\n            // Only animate on desktop\n            if (!isMobile) {\n                // First animate the widget out\n                setWidgetAnimation(\"animate-slide-down\");\n                setIndicatorAnimation(\"\"); // Reset indicator animation\n                setTimeout(()=>{\n                    setIsOpen(false);\n                    setWidgetAnimation(\"\");\n                    setButtonAnimation(\"animate-slide-up\");\n                    setIndicatorAnimation(\"animate-slide-up\"); // Apply same animation as button\n                }, 300);\n            } else {\n                // Immediate close on mobile\n                setIsOpen(false);\n            }\n        } else {\n            // Opening the chat\n            if (!isMobile) {\n                setButtonAnimation(\"animate-slide-down\");\n                setIndicatorAnimation(\"animate-slide-down\"); // Apply same animation as button\n                setTimeout(()=>{\n                    setIsOpen(true);\n                    setWidgetAnimation(\"animate-slide-up\");\n                }, 300);\n            } else {\n                // Immediate open on mobile\n                setIsOpen(true);\n            }\n        }\n    };\n    // Event handlers\n    const handleSendMessage = (e)=>{\n        e.preventDefault();\n        if (!inputMessage.trim() || !isConnected || !hasConsented) {\n            console.log(\"Cannot send message:\", {\n                hasMessage: !!inputMessage.trim(),\n                isConnected,\n                hasConsented\n            });\n            return;\n        }\n        const messageContent = inputMessage.trim();\n        pendingMessageRef.current = messageContent;\n        console.log(\"Sending message:\", messageContent, \"to chat:\", chatId);\n        const newMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n            content: messageContent,\n            timestamp: new Date().toISOString(),\n            role: \"user\"\n        };\n        setMessages((prev)=>{\n            const updatedMessages = [\n                ...prev,\n                newMessage\n            ];\n            messagesRef.current = updatedMessages;\n            return updatedMessages;\n        });\n        setInputMessage(\"\");\n        setIsBotTyping(true);\n        setTimeout(scrollToBottom, 100);\n        // Send message to server with better error handling\n        if (socketRef.current) {\n            socketRef.current.emit(\"send-message\", {\n                chatId,\n                content: messageContent\n            });\n            console.log(\"Message sent to server\");\n        } else {\n            console.error(\"Socket not available\");\n            setIsBotTyping(false);\n        }\n    };\n    // Show reset confirmation dialog\n    const showResetConfirmation = ()=>{\n        if (!isConnected || isResetting) return;\n        setShowResetConfirm(true);\n    };\n    // Cancel reset\n    const cancelReset = ()=>{\n        setShowResetConfirm(false);\n    };\n    // Handle chat reset\n    const handleResetChat = ()=>{\n        var // Emit reset-chat event to the server\n        _socketRef_current;\n        if (!isConnected || isResetting) return;\n        setShowResetConfirm(false);\n        setIsResetting(true);\n        setMessages([]);\n        // Generate a new UUID for the new chat\n        const newChatId = (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n        (_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.emit(\"reset-chat\", {\n            chatId,\n            newChatId\n        });\n    };\n    // UI Components\n    const TypingAnimation = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-1 items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2 w-2 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                        animationDelay: \"0.1s\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2 w-2 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                        animationDelay: \"0.2s\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2 w-2 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                        animationDelay: \"0.3s\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 507,\n            columnNumber: 5\n        }, undefined);\n    const LoadingState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-8 h-8 animate-spin text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: isResetting ? \"Mengatur ulang obrolan...\" : \"Menyiapkan asisten obrolan Anda...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 524,\n            columnNumber: 5\n        }, undefined);\n    // Reset confirmation dialog\n    const ResetConfirmDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0 bg-black bg-opacity-50 z-10 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Reset Percakapan\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mb-6\",\n                        children: \"Apakah Anda yakin ingin mengatur ulang percakapan ini? Semua riwayat obrolan akan dihapus dan tidak dapat dikembalikan.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: cancelReset,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n                                children: \"Batal\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleResetChat,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n            lineNumber: 536,\n            columnNumber: 5\n        }, undefined);\n    // Determine if connection indicator should be visible\n    // Only show when the chat is closed AND user has consented\n    const showConnectionIndicator = hasConsented && (!isOpen || indicatorAnimation !== \"\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            showConnectionIndicator && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 right-6 z-50 \".concat(indicatorAnimation),\n                style: {\n                    display: isOpen && indicatorAnimation === \"\" ? \"none\" : \"block\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center p-1 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-red-500\", \" w-3 h-3\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 572,\n                columnNumber: 9\n            }, undefined),\n            (!isOpen || !isMobile && buttonAnimation !== \"\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleChat,\n                className: \"fixed bottom-6 right-6 p-4 rounded-full shadow-lg z-50 transition-all bg-blue-500 hover:bg-blue-600 text-white \".concat(!isMobile ? buttonAnimation : \"\"),\n                \"aria-label\": \"Toggle chat widget\",\n                style: {\n                    display: isOpen && buttonAnimation === \"\" && !isMobile ? \"none\" : \"block\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-6 h-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 588,\n                columnNumber: 9\n            }, undefined),\n            (isOpen || !isMobile && widgetAnimation === \"animate-slide-down\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bg-white border border-gray-200 flex flex-col overflow-hidden z-[100] \".concat(isMobile ? \"inset-0 m-0 rounded-none\" : \"bottom-0 right-6 w-[500px] h-[600px] rounded-t-lg shadow-xl \".concat(widgetAnimation)),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-600 text-white border-b flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-400 rounded-full overflow-hidden mr-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: AVATAR_URL,\n                                            alt: \"Asisten Virtual\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-sm\",\n                                                children: \"Asisten Virtual DPMPTSP\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            hasConsented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full mr-1 \".concat(isConnected ? \"bg-green-400\" : \"bg-red-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: isConnected ? \"Online\" : \"Offline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: showResetConfirmation,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        \"aria-label\": \"Reset chat\",\n                                        disabled: !isConnected || isResetting,\n                                        title: \"Reset percakapan\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 18,\n                                            className: isResetting ? \"animate-spin\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleChat,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        \"aria-label\": \"Close chat\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 pl-4 bg-gray-50 overflow-hidden relative\",\n                        children: [\n                            showResetConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResetConfirmDialog, {}, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 34\n                            }, undefined),\n                            isInitializing || isResetting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingState, {}, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 15\n                            }, undefined) : !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 text-center max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-500 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                className: \"mx-auto mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"18\",\n                                                        y1: \"6\",\n                                                        x2: \"6\",\n                                                        y2: \"18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"6\",\n                                                        y1: \"6\",\n                                                        x2: \"18\",\n                                                        y2: \"18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-red-700 mb-1\",\n                                            children: \"Koneksi Teputus\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: \"Chatbot saat ini sedang offline. Harap segarkan halaman atau periksa koneksi internet Anda.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto pr-4 chat-messages-container\",\n                                ref: chatContainerRef,\n                                children: [\n                                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full text-gray-500 text-sm\",\n                                        children: \"Obrolan kosong. Ketik sesuatu untuk memulai percakapan!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 19\n                                    }, undefined) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 \".concat(message.role === \"user\" ? \"text-right\" : \"text-left\"),\n                                            children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: AVATAR_URL,\n                                                            alt: \"AI Assistant\",\n                                                            className: \"w-full h-full object-cover bg-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-[75%]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-2 rounded-lg text-sm \".concat(message.status === \"error\" ? \"bg-red-50 text-red-800 border border-red-200\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: [\n                                                                    message.status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mb-1 text-red-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                size: 14,\n                                                                                className: \"mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-medium\",\n                                                                                children: \"Error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 33\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"whitespace-pre-wrap\",\n                                                                        children: message.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    message.status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2 text-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"inline-flex items-center px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-800 rounded transition-colors\",\n                                                                            onClick: ()=>{\n                                                                                // Find the last user message before this error\n                                                                                const lastUserMsgIndex = [\n                                                                                    ...messages\n                                                                                ].reverse().findIndex((msg)=>msg.role === \"user\");\n                                                                                if (lastUserMsgIndex >= 0) {\n                                                                                    var _socketRef_current;\n                                                                                    const lastUserMsg = messages[messages.length - 1 - lastUserMsgIndex];\n                                                                                    (_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.emit(\"send-message\", {\n                                                                                        chatId,\n                                                                                        content: lastUserMsg.content,\n                                                                                        isRetry: true\n                                                                                    });\n                                                                                    setIsBotTyping(true);\n                                                                                }\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    size: 12,\n                                                                                    className: \"mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                                    lineNumber: 786,\n                                                                                    columnNumber: 37\n                                                                                }, undefined),\n                                                                                \" \",\n                                                                                \"Coba Lagi\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 ml-1 mt-1\",\n                                                                children: formatTimestamp(message.timestamp)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block px-3 py-2 rounded-lg bg-blue-600 text-white text-sm max-w-[75%]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-wrap\",\n                                                            children: message.content\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 text-right mt-1\",\n                                                        children: formatTimestamp(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, message.id, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 21\n                                        }, undefined)),\n                                    isBotTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: AVATAR_URL,\n                                                        alt: \"AI Assistant\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 rounded-lg bg-gray-100 text-gray-800 text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingAnimation, {}, void 0, false, {\n                                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 ml-1 mt-1\",\n                                                            children: formatTimestamp(new Date().toISOString())\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 15\n                            }, undefined),\n                            hasConsented && showScrollButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: scrollToBottom,\n                                className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 rounded-full bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white p-2 transition-all shadow-md\",\n                                \"aria-label\": \"Scroll to bottom\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_Loader2_RefreshCw_RotateCcw_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, undefined),\n                    hasConsented && !isInitializing && !isResetting && isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSendMessage,\n                        className: \"p-3 border-t border-gray-200 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center bg-gray-100 rounded-full px-3 py-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: inputMessage,\n                                    onChange: (e)=>setInputMessage(e.target.value),\n                                    placeholder: \"Type a reply...\",\n                                    className: \"flex-1 p-2 bg-transparent border-none focus:outline-none text-sm text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"p-2 text-blue-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"m22 2-7 20-4-9-9-4Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M22 2 11 13\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                            lineNumber: 855,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                        lineNumber: 851,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\ChatWidget.tsx\",\n                lineNumber: 618,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ChatWidget, \"OX73vrCSF6IdobMw3jw30ECHIRY=\");\n_c = ChatWidget;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatWidget);\nvar _c;\n$RefreshReg$(_c, \"ChatWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatWidget.tsx\n"));

/***/ })

});