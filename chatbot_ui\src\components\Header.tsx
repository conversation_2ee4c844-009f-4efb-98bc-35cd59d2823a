"use client";

import Link from "next/link";
import {
  FaPhoneAlt,
  FaEnvelope,
  FaBars,
  FaSearch,
  FaWhatsapp,
} from "react-icons/fa";
import { useState } from "react";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  return (
    <header className="bg-gray-900 text-white">
      <div className="container mx-auto">
        {/* Mobile Header */}
        <div className="lg:hidden flex justify-between items-center py-3 px-4">
          <div className="text-center text-sm">
            Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu Kota
            Pekanbaru
          </div>
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="bg-red-500 px-3 py-2 border rounded"
          >
            <svg
              className="fill-current h-5 w-5"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>Menu</title>
              <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z" />
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`md:hidden transition-all duration-300 ${
            isMenuOpen
              ? "max-h-screen opacity-100"
              : "max-h-0 opacity-0 overflow-hidden"
          }`}
        >
          <div className="px-4 py-2 border-t border-gray-700">
            <Link
              href="tel:08137536001"
              className="flex items-center py-3 border-b border-gray-700"
            >
              <FaPhoneAlt size={16} className="mr-3" />
              <span>DPMPTSP Pekanbaru</span>
            </Link>
            <Link
              href="mailto:08137536001"
              className="flex items-center py-3 border-b border-gray-700"
            >
              <FaEnvelope size={16} className="mr-3" />
              <span>08137536001</span>
            </Link>
            <Link
              href="https://wa.me/08137536001"
              className="flex items-center py-3 border-b border-gray-700"
            >
              <FaWhatsapp size={16} className="mr-3" />
              <span>08137536001</span>
            </Link>
          </div>
          <div className="px-4 py-3">
            <div className="flex">
              <input
                type="text"
                placeholder="cari..."
                className="w-full p-2 text-black rounded-l-md focus:outline-none"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button
                className="bg-red-500 text-white px-6 rounded-r-md"
                onClick={() => {
                  console.log("Searching:", searchQuery);
                }}
              >
                <span className="hidden sm:inline">Search</span>
                <FaSearch size={16} className="sm:hidden" />
              </button>
            </div>
          </div>
        </div>

        {/* Desktop Header */}
        <div className="hidden lg:block">
          <div className="flex items-center justify-between py-2 px-4">
            <div className="text-sm">
              Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu Kota
              Pekanbaru
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="tel:08137536001"
                className="flex items-center space-x-2"
              >
                <FaPhoneAlt size={16} />
                <span>DPMPTSP Pekanbaru</span>
              </Link>
              <Link
                href="mailto:08137536001"
                className="flex items-center space-x-2"
              >
                <FaEnvelope size={16} />
                <span>08137536001</span>
              </Link>
              <Link
                href="https://wa.me/08137536001"
                className="flex items-center space-x-2"
              >
                <FaWhatsapp size={16} />
                <span>08137536001</span>
              </Link>
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="text-white hover:text-gray-300"
              >
                <FaSearch size={16} />
              </button>
            </div>
          </div>

          {/* Desktop Search - Only shows when clicked */}
          {isSearchOpen && (
            <div className="bg-gray-800 py-2 px-4 transition-all duration-300">
              <div className="flex">
                <input
                  type="text"
                  placeholder="cari..."
                  className="w-full max-w-md p-2 text-black rounded-l-md focus:outline-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  className="bg-red-500 text-white px-6 rounded-r-md"
                  onClick={() => {
                    console.log("Searching:", searchQuery);
                    setIsSearchOpen(false);
                  }}
                >
                  Search
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
