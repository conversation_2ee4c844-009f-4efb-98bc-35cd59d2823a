/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchatbot%20fe%20n%20be%5C%5Cchatbot_ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchatbot%20fe%20n%20be%5C%5Cchatbot_ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NoYXRib3QlMjBmZSUyMG4lMjBiZSU1QyU1Q2NoYXRib3RfdWklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcY2hhdGJvdCBmZSBuIGJlXFxcXGNoYXRib3RfdWlcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchatbot%20fe%20n%20be%5C%5Cchatbot_ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/image.js":
/*!*********************************************!*\
  !*** ./node_modules/next/dist/api/image.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/image-external */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js\");\n/* harmony import */ var _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _shared_lib_image_external__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=image.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2ltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RDtBQUNWOztBQUU3QyIsInNvdXJjZXMiOlsiRDpcXGNoYXRib3QgZmUgbiBiZVxcY2hhdGJvdF91aVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXGltYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuLi9zaGFyZWQvbGliL2ltYWdlLWV4dGVybmFsJztcbmV4cG9ydCAqIGZyb20gJy4uL3NoYXJlZC9saWIvaW1hZ2UtZXh0ZXJuYWwnO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbWFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/image.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/picomatch/index.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n(()=>{\"use strict\";var t={170:(t,e,u)=>{const n=u(510);const isWindows=()=>{if(typeof navigator!==\"undefined\"&&navigator.platform){const t=navigator.platform.toLowerCase();return t===\"win32\"||t===\"windows\"}if(typeof process!==\"undefined\"&&process.platform){return process.platform===\"win32\"}return false};function picomatch(t,e,u=false){if(e&&(e.windows===null||e.windows===undefined)){e={...e,windows:isWindows()}}return n(t,e,u)}Object.assign(picomatch,n);t.exports=picomatch},154:t=>{const e=\"\\\\\\\\/\";const u=`[^${e}]`;const n=\"\\\\.\";const o=\"\\\\+\";const s=\"\\\\?\";const r=\"\\\\/\";const a=\"(?=.)\";const i=\"[^/]\";const c=`(?:${r}|$)`;const p=`(?:^|${r})`;const l=`${n}{1,2}${c}`;const f=`(?!${n})`;const A=`(?!${p}${l})`;const _=`(?!${n}{0,1}${c})`;const R=`(?!${l})`;const E=`[^.${r}]`;const h=`${i}*?`;const g=\"/\";const b={DOT_LITERAL:n,PLUS_LITERAL:o,QMARK_LITERAL:s,SLASH_LITERAL:r,ONE_CHAR:a,QMARK:i,END_ANCHOR:c,DOTS_SLASH:l,NO_DOT:f,NO_DOTS:A,NO_DOT_SLASH:_,NO_DOTS_SLASH:R,QMARK_NO_DOT:E,STAR:h,START_ANCHOR:p,SEP:g};const C={...b,SLASH_LITERAL:`[${e}]`,QMARK:u,STAR:`${u}*?`,DOTS_SLASH:`${n}{1,2}(?:[${e}]|$)`,NO_DOT:`(?!${n})`,NO_DOTS:`(?!(?:^|[${e}])${n}{1,2}(?:[${e}]|$))`,NO_DOT_SLASH:`(?!${n}{0,1}(?:[${e}]|$))`,NO_DOTS_SLASH:`(?!${n}{1,2}(?:[${e}]|$))`,QMARK_NO_DOT:`[^.${e}]`,START_ANCHOR:`(?:^|[${e}])`,END_ANCHOR:`(?:[${e}]|$)`,SEP:\"\\\\\"};const y={alnum:\"a-zA-Z0-9\",alpha:\"a-zA-Z\",ascii:\"\\\\x00-\\\\x7F\",blank:\" \\\\t\",cntrl:\"\\\\x00-\\\\x1F\\\\x7F\",digit:\"0-9\",graph:\"\\\\x21-\\\\x7E\",lower:\"a-z\",print:\"\\\\x20-\\\\x7E \",punct:\"\\\\-!\\\"#$%&'()\\\\*+,./:;<=>?@[\\\\]^_`{|}~\",space:\" \\\\t\\\\r\\\\n\\\\v\\\\f\",upper:\"A-Z\",word:\"A-Za-z0-9_\",xdigit:\"A-Fa-f0-9\"};t.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:y,REGEX_BACKSLASH:/\\\\(?![*+?^${}(|)[\\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\\].,$*+?^{}()|\\\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\\\?)((\\W)(\\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\\[.*?[^\\\\]\\]|\\\\(?=.))/g,REPLACEMENTS:{\"***\":\"*\",\"**/**\":\"**\",\"**/**/**\":\"**\"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,extglobChars(t){return{\"!\":{type:\"negate\",open:\"(?:(?!(?:\",close:`))${t.STAR})`},\"?\":{type:\"qmark\",open:\"(?:\",close:\")?\"},\"+\":{type:\"plus\",open:\"(?:\",close:\")+\"},\"*\":{type:\"star\",open:\"(?:\",close:\")*\"},\"@\":{type:\"at\",open:\"(?:\",close:\")\"}}},globChars(t){return t===true?C:b}}},697:(t,e,u)=>{const n=u(154);const o=u(96);const{MAX_LENGTH:s,POSIX_REGEX_SOURCE:r,REGEX_NON_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_BACKREF:i,REPLACEMENTS:c}=n;const expandRange=(t,e)=>{if(typeof e.expandRange===\"function\"){return e.expandRange(...t,e)}t.sort();const u=`[${t.join(\"-\")}]`;try{new RegExp(u)}catch(e){return t.map((t=>o.escapeRegex(t))).join(\"..\")}return u};const syntaxError=(t,e)=>`Missing ${t}: \"${e}\" - use \"\\\\\\\\${e}\" to match literal characters`;const parse=(t,e)=>{if(typeof t!==\"string\"){throw new TypeError(\"Expected a string\")}t=c[t]||t;const u={...e};const p=typeof u.maxLength===\"number\"?Math.min(s,u.maxLength):s;let l=t.length;if(l>p){throw new SyntaxError(`Input length: ${l}, exceeds maximum allowed length: ${p}`)}const f={type:\"bos\",value:\"\",output:u.prepend||\"\"};const A=[f];const _=u.capture?\"\":\"?:\";const R=n.globChars(u.windows);const E=n.extglobChars(R);const{DOT_LITERAL:h,PLUS_LITERAL:g,SLASH_LITERAL:b,ONE_CHAR:C,DOTS_SLASH:y,NO_DOT:$,NO_DOT_SLASH:x,NO_DOTS_SLASH:S,QMARK:H,QMARK_NO_DOT:v,STAR:d,START_ANCHOR:L}=R;const globstar=t=>`(${_}(?:(?!${L}${t.dot?y:h}).)*?)`;const T=u.dot?\"\":$;const O=u.dot?H:v;let k=u.bash===true?globstar(u):d;if(u.capture){k=`(${k})`}if(typeof u.noext===\"boolean\"){u.noextglob=u.noext}const m={input:t,index:-1,start:0,dot:u.dot===true,consumed:\"\",output:\"\",prefix:\"\",backtrack:false,negated:false,brackets:0,braces:0,parens:0,quotes:0,globstar:false,tokens:A};t=o.removePrefix(t,m);l=t.length;const w=[];const N=[];const I=[];let B=f;let G;const eos=()=>m.index===l-1;const D=m.peek=(e=1)=>t[m.index+e];const M=m.advance=()=>t[++m.index]||\"\";const remaining=()=>t.slice(m.index+1);const consume=(t=\"\",e=0)=>{m.consumed+=t;m.index+=e};const append=t=>{m.output+=t.output!=null?t.output:t.value;consume(t.value)};const negate=()=>{let t=1;while(D()===\"!\"&&(D(2)!==\"(\"||D(3)===\"?\")){M();m.start++;t++}if(t%2===0){return false}m.negated=true;m.start++;return true};const increment=t=>{m[t]++;I.push(t)};const decrement=t=>{m[t]--;I.pop()};const push=t=>{if(B.type===\"globstar\"){const e=m.braces>0&&(t.type===\"comma\"||t.type===\"brace\");const u=t.extglob===true||w.length&&(t.type===\"pipe\"||t.type===\"paren\");if(t.type!==\"slash\"&&t.type!==\"paren\"&&!e&&!u){m.output=m.output.slice(0,-B.output.length);B.type=\"star\";B.value=\"*\";B.output=k;m.output+=B.output}}if(w.length&&t.type!==\"paren\"){w[w.length-1].inner+=t.value}if(t.value||t.output)append(t);if(B&&B.type===\"text\"&&t.type===\"text\"){B.output=(B.output||B.value)+t.value;B.value+=t.value;return}t.prev=B;A.push(t);B=t};const extglobOpen=(t,e)=>{const n={...E[e],conditions:1,inner:\"\"};n.prev=B;n.parens=m.parens;n.output=m.output;const o=(u.capture?\"(\":\"\")+n.open;increment(\"parens\");push({type:t,value:e,output:m.output?\"\":C});push({type:\"paren\",extglob:true,value:M(),output:o});w.push(n)};const extglobClose=t=>{let n=t.close+(u.capture?\")\":\"\");let o;if(t.type===\"negate\"){let s=k;if(t.inner&&t.inner.length>1&&t.inner.includes(\"/\")){s=globstar(u)}if(s!==k||eos()||/^\\)+$/.test(remaining())){n=t.close=`)$))${s}`}if(t.inner.includes(\"*\")&&(o=remaining())&&/^\\.[^\\\\/.]+$/.test(o)){const u=parse(o,{...e,fastpaths:false}).output;n=t.close=`)${u})${s})`}if(t.prev.type===\"bos\"){m.negatedExtglob=true}}push({type:\"paren\",extglob:true,value:G,output:n});decrement(\"parens\")};if(u.fastpaths!==false&&!/(^[*!]|[/()[\\]{}\"])/.test(t)){let n=false;let s=t.replace(i,((t,e,u,o,s,r)=>{if(o===\"\\\\\"){n=true;return t}if(o===\"?\"){if(e){return e+o+(s?H.repeat(s.length):\"\")}if(r===0){return O+(s?H.repeat(s.length):\"\")}return H.repeat(u.length)}if(o===\".\"){return h.repeat(u.length)}if(o===\"*\"){if(e){return e+o+(s?k:\"\")}return k}return e?t:`\\\\${t}`}));if(n===true){if(u.unescape===true){s=s.replace(/\\\\/g,\"\")}else{s=s.replace(/\\\\+/g,(t=>t.length%2===0?\"\\\\\\\\\":t?\"\\\\\":\"\"))}}if(s===t&&u.contains===true){m.output=t;return m}m.output=o.wrapOutput(s,m,e);return m}while(!eos()){G=M();if(G===\"\\0\"){continue}if(G===\"\\\\\"){const t=D();if(t===\"/\"&&u.bash!==true){continue}if(t===\".\"||t===\";\"){continue}if(!t){G+=\"\\\\\";push({type:\"text\",value:G});continue}const e=/^\\\\+/.exec(remaining());let n=0;if(e&&e[0].length>2){n=e[0].length;m.index+=n;if(n%2!==0){G+=\"\\\\\"}}if(u.unescape===true){G=M()}else{G+=M()}if(m.brackets===0){push({type:\"text\",value:G});continue}}if(m.brackets>0&&(G!==\"]\"||B.value===\"[\"||B.value===\"[^\")){if(u.posix!==false&&G===\":\"){const t=B.value.slice(1);if(t.includes(\"[\")){B.posix=true;if(t.includes(\":\")){const t=B.value.lastIndexOf(\"[\");const e=B.value.slice(0,t);const u=B.value.slice(t+2);const n=r[u];if(n){B.value=e+n;m.backtrack=true;M();if(!f.output&&A.indexOf(B)===1){f.output=C}continue}}}}if(G===\"[\"&&D()!==\":\"||G===\"-\"&&D()===\"]\"){G=`\\\\${G}`}if(G===\"]\"&&(B.value===\"[\"||B.value===\"[^\")){G=`\\\\${G}`}if(u.posix===true&&G===\"!\"&&B.value===\"[\"){G=\"^\"}B.value+=G;append({value:G});continue}if(m.quotes===1&&G!=='\"'){G=o.escapeRegex(G);B.value+=G;append({value:G});continue}if(G==='\"'){m.quotes=m.quotes===1?0:1;if(u.keepQuotes===true){push({type:\"text\",value:G})}continue}if(G===\"(\"){increment(\"parens\");push({type:\"paren\",value:G});continue}if(G===\")\"){if(m.parens===0&&u.strictBrackets===true){throw new SyntaxError(syntaxError(\"opening\",\"(\"))}const t=w[w.length-1];if(t&&m.parens===t.parens+1){extglobClose(w.pop());continue}push({type:\"paren\",value:G,output:m.parens?\")\":\"\\\\)\"});decrement(\"parens\");continue}if(G===\"[\"){if(u.nobracket===true||!remaining().includes(\"]\")){if(u.nobracket!==true&&u.strictBrackets===true){throw new SyntaxError(syntaxError(\"closing\",\"]\"))}G=`\\\\${G}`}else{increment(\"brackets\")}push({type:\"bracket\",value:G});continue}if(G===\"]\"){if(u.nobracket===true||B&&B.type===\"bracket\"&&B.value.length===1){push({type:\"text\",value:G,output:`\\\\${G}`});continue}if(m.brackets===0){if(u.strictBrackets===true){throw new SyntaxError(syntaxError(\"opening\",\"[\"))}push({type:\"text\",value:G,output:`\\\\${G}`});continue}decrement(\"brackets\");const t=B.value.slice(1);if(B.posix!==true&&t[0]===\"^\"&&!t.includes(\"/\")){G=`/${G}`}B.value+=G;append({value:G});if(u.literalBrackets===false||o.hasRegexChars(t)){continue}const e=o.escapeRegex(B.value);m.output=m.output.slice(0,-B.value.length);if(u.literalBrackets===true){m.output+=e;B.value=e;continue}B.value=`(${_}${e}|${B.value})`;m.output+=B.value;continue}if(G===\"{\"&&u.nobrace!==true){increment(\"braces\");const t={type:\"brace\",value:G,output:\"(\",outputIndex:m.output.length,tokensIndex:m.tokens.length};N.push(t);push(t);continue}if(G===\"}\"){const t=N[N.length-1];if(u.nobrace===true||!t){push({type:\"text\",value:G,output:G});continue}let e=\")\";if(t.dots===true){const t=A.slice();const n=[];for(let e=t.length-1;e>=0;e--){A.pop();if(t[e].type===\"brace\"){break}if(t[e].type!==\"dots\"){n.unshift(t[e].value)}}e=expandRange(n,u);m.backtrack=true}if(t.comma!==true&&t.dots!==true){const u=m.output.slice(0,t.outputIndex);const n=m.tokens.slice(t.tokensIndex);t.value=t.output=\"\\\\{\";G=e=\"\\\\}\";m.output=u;for(const t of n){m.output+=t.output||t.value}}push({type:\"brace\",value:G,output:e});decrement(\"braces\");N.pop();continue}if(G===\"|\"){if(w.length>0){w[w.length-1].conditions++}push({type:\"text\",value:G});continue}if(G===\",\"){let t=G;const e=N[N.length-1];if(e&&I[I.length-1]===\"braces\"){e.comma=true;t=\"|\"}push({type:\"comma\",value:G,output:t});continue}if(G===\"/\"){if(B.type===\"dot\"&&m.index===m.start+1){m.start=m.index+1;m.consumed=\"\";m.output=\"\";A.pop();B=f;continue}push({type:\"slash\",value:G,output:b});continue}if(G===\".\"){if(m.braces>0&&B.type===\"dot\"){if(B.value===\".\")B.output=h;const t=N[N.length-1];B.type=\"dots\";B.output+=G;B.value+=G;t.dots=true;continue}if(m.braces+m.parens===0&&B.type!==\"bos\"&&B.type!==\"slash\"){push({type:\"text\",value:G,output:h});continue}push({type:\"dot\",value:G,output:h});continue}if(G===\"?\"){const t=B&&B.value===\"(\";if(!t&&u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){extglobOpen(\"qmark\",G);continue}if(B&&B.type===\"paren\"){const t=D();let e=G;if(B.value===\"(\"&&!/[!=<:]/.test(t)||t===\"<\"&&!/<([!=]|\\w+>)/.test(remaining())){e=`\\\\${G}`}push({type:\"text\",value:G,output:e});continue}if(u.dot!==true&&(B.type===\"slash\"||B.type===\"bos\")){push({type:\"qmark\",value:G,output:v});continue}push({type:\"qmark\",value:G,output:H});continue}if(G===\"!\"){if(u.noextglob!==true&&D()===\"(\"){if(D(2)!==\"?\"||!/[!=<:]/.test(D(3))){extglobOpen(\"negate\",G);continue}}if(u.nonegate!==true&&m.index===0){negate();continue}}if(G===\"+\"){if(u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){extglobOpen(\"plus\",G);continue}if(B&&B.value===\"(\"||u.regex===false){push({type:\"plus\",value:G,output:g});continue}if(B&&(B.type===\"bracket\"||B.type===\"paren\"||B.type===\"brace\")||m.parens>0){push({type:\"plus\",value:G});continue}push({type:\"plus\",value:g});continue}if(G===\"@\"){if(u.noextglob!==true&&D()===\"(\"&&D(2)!==\"?\"){push({type:\"at\",extglob:true,value:G,output:\"\"});continue}push({type:\"text\",value:G});continue}if(G!==\"*\"){if(G===\"$\"||G===\"^\"){G=`\\\\${G}`}const t=a.exec(remaining());if(t){G+=t[0];m.index+=t[0].length}push({type:\"text\",value:G});continue}if(B&&(B.type===\"globstar\"||B.star===true)){B.type=\"star\";B.star=true;B.value+=G;B.output=k;m.backtrack=true;m.globstar=true;consume(G);continue}let e=remaining();if(u.noextglob!==true&&/^\\([^?]/.test(e)){extglobOpen(\"star\",G);continue}if(B.type===\"star\"){if(u.noglobstar===true){consume(G);continue}const n=B.prev;const o=n.prev;const s=n.type===\"slash\"||n.type===\"bos\";const r=o&&(o.type===\"star\"||o.type===\"globstar\");if(u.bash===true&&(!s||e[0]&&e[0]!==\"/\")){push({type:\"star\",value:G,output:\"\"});continue}const a=m.braces>0&&(n.type===\"comma\"||n.type===\"brace\");const i=w.length&&(n.type===\"pipe\"||n.type===\"paren\");if(!s&&n.type!==\"paren\"&&!a&&!i){push({type:\"star\",value:G,output:\"\"});continue}while(e.slice(0,3)===\"/**\"){const u=t[m.index+4];if(u&&u!==\"/\"){break}e=e.slice(3);consume(\"/**\",3)}if(n.type===\"bos\"&&eos()){B.type=\"globstar\";B.value+=G;B.output=globstar(u);m.output=B.output;m.globstar=true;consume(G);continue}if(n.type===\"slash\"&&n.prev.type!==\"bos\"&&!r&&eos()){m.output=m.output.slice(0,-(n.output+B.output).length);n.output=`(?:${n.output}`;B.type=\"globstar\";B.output=globstar(u)+(u.strictSlashes?\")\":\"|$)\");B.value+=G;m.globstar=true;m.output+=n.output+B.output;consume(G);continue}if(n.type===\"slash\"&&n.prev.type!==\"bos\"&&e[0]===\"/\"){const t=e[1]!==void 0?\"|$\":\"\";m.output=m.output.slice(0,-(n.output+B.output).length);n.output=`(?:${n.output}`;B.type=\"globstar\";B.output=`${globstar(u)}${b}|${b}${t})`;B.value+=G;m.output+=n.output+B.output;m.globstar=true;consume(G+M());push({type:\"slash\",value:\"/\",output:\"\"});continue}if(n.type===\"bos\"&&e[0]===\"/\"){B.type=\"globstar\";B.value+=G;B.output=`(?:^|${b}|${globstar(u)}${b})`;m.output=B.output;m.globstar=true;consume(G+M());push({type:\"slash\",value:\"/\",output:\"\"});continue}m.output=m.output.slice(0,-B.output.length);B.type=\"globstar\";B.output=globstar(u);B.value+=G;m.output+=B.output;m.globstar=true;consume(G);continue}const n={type:\"star\",value:G,output:k};if(u.bash===true){n.output=\".*?\";if(B.type===\"bos\"||B.type===\"slash\"){n.output=T+n.output}push(n);continue}if(B&&(B.type===\"bracket\"||B.type===\"paren\")&&u.regex===true){n.output=G;push(n);continue}if(m.index===m.start||B.type===\"slash\"||B.type===\"dot\"){if(B.type===\"dot\"){m.output+=x;B.output+=x}else if(u.dot===true){m.output+=S;B.output+=S}else{m.output+=T;B.output+=T}if(D()!==\"*\"){m.output+=C;B.output+=C}}push(n)}while(m.brackets>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\"]\"));m.output=o.escapeLast(m.output,\"[\");decrement(\"brackets\")}while(m.parens>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\")\"));m.output=o.escapeLast(m.output,\"(\");decrement(\"parens\")}while(m.braces>0){if(u.strictBrackets===true)throw new SyntaxError(syntaxError(\"closing\",\"}\"));m.output=o.escapeLast(m.output,\"{\");decrement(\"braces\")}if(u.strictSlashes!==true&&(B.type===\"star\"||B.type===\"bracket\")){push({type:\"maybe_slash\",value:\"\",output:`${b}?`})}if(m.backtrack===true){m.output=\"\";for(const t of m.tokens){m.output+=t.output!=null?t.output:t.value;if(t.suffix){m.output+=t.suffix}}}return m};parse.fastpaths=(t,e)=>{const u={...e};const r=typeof u.maxLength===\"number\"?Math.min(s,u.maxLength):s;const a=t.length;if(a>r){throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${r}`)}t=c[t]||t;const{DOT_LITERAL:i,SLASH_LITERAL:p,ONE_CHAR:l,DOTS_SLASH:f,NO_DOT:A,NO_DOTS:_,NO_DOTS_SLASH:R,STAR:E,START_ANCHOR:h}=n.globChars(u.windows);const g=u.dot?_:A;const b=u.dot?R:A;const C=u.capture?\"\":\"?:\";const y={negated:false,prefix:\"\"};let $=u.bash===true?\".*?\":E;if(u.capture){$=`(${$})`}const globstar=t=>{if(t.noglobstar===true)return $;return`(${C}(?:(?!${h}${t.dot?f:i}).)*?)`};const create=t=>{switch(t){case\"*\":return`${g}${l}${$}`;case\".*\":return`${i}${l}${$}`;case\"*.*\":return`${g}${$}${i}${l}${$}`;case\"*/*\":return`${g}${$}${p}${l}${b}${$}`;case\"**\":return g+globstar(u);case\"**/*\":return`(?:${g}${globstar(u)}${p})?${b}${l}${$}`;case\"**/*.*\":return`(?:${g}${globstar(u)}${p})?${b}${$}${i}${l}${$}`;case\"**/.*\":return`(?:${g}${globstar(u)}${p})?${i}${l}${$}`;default:{const e=/^(.*?)\\.(\\w+)$/.exec(t);if(!e)return;const u=create(e[1]);if(!u)return;return u+i+e[2]}}};const x=o.removePrefix(t,y);let S=create(x);if(S&&u.strictSlashes!==true){S+=`${p}?`}return S};t.exports=parse},510:(t,e,u)=>{const n=u(716);const o=u(697);const s=u(96);const r=u(154);const isObject=t=>t&&typeof t===\"object\"&&!Array.isArray(t);const picomatch=(t,e,u=false)=>{if(Array.isArray(t)){const n=t.map((t=>picomatch(t,e,u)));const arrayMatcher=t=>{for(const e of n){const u=e(t);if(u)return u}return false};return arrayMatcher}const n=isObject(t)&&t.tokens&&t.input;if(t===\"\"||typeof t!==\"string\"&&!n){throw new TypeError(\"Expected pattern to be a non-empty string\")}const o=e||{};const s=o.windows;const r=n?picomatch.compileRe(t,e):picomatch.makeRe(t,e,false,true);const a=r.state;delete r.state;let isIgnored=()=>false;if(o.ignore){const t={...e,ignore:null,onMatch:null,onResult:null};isIgnored=picomatch(o.ignore,t,u)}const matcher=(u,n=false)=>{const{isMatch:i,match:c,output:p}=picomatch.test(u,r,e,{glob:t,posix:s});const l={glob:t,state:a,regex:r,posix:s,input:u,output:p,match:c,isMatch:i};if(typeof o.onResult===\"function\"){o.onResult(l)}if(i===false){l.isMatch=false;return n?l:false}if(isIgnored(u)){if(typeof o.onIgnore===\"function\"){o.onIgnore(l)}l.isMatch=false;return n?l:false}if(typeof o.onMatch===\"function\"){o.onMatch(l)}return n?l:true};if(u){matcher.state=a}return matcher};picomatch.test=(t,e,u,{glob:n,posix:o}={})=>{if(typeof t!==\"string\"){throw new TypeError(\"Expected input to be a string\")}if(t===\"\"){return{isMatch:false,output:\"\"}}const r=u||{};const a=r.format||(o?s.toPosixSlashes:null);let i=t===n;let c=i&&a?a(t):t;if(i===false){c=a?a(t):t;i=c===n}if(i===false||r.capture===true){if(r.matchBase===true||r.basename===true){i=picomatch.matchBase(t,e,u,o)}else{i=e.exec(c)}}return{isMatch:Boolean(i),match:i,output:c}};picomatch.matchBase=(t,e,u)=>{const n=e instanceof RegExp?e:picomatch.makeRe(e,u);return n.test(s.basename(t))};picomatch.isMatch=(t,e,u)=>picomatch(e,u)(t);picomatch.parse=(t,e)=>{if(Array.isArray(t))return t.map((t=>picomatch.parse(t,e)));return o(t,{...e,fastpaths:false})};picomatch.scan=(t,e)=>n(t,e);picomatch.compileRe=(t,e,u=false,n=false)=>{if(u===true){return t.output}const o=e||{};const s=o.contains?\"\":\"^\";const r=o.contains?\"\":\"$\";let a=`${s}(?:${t.output})${r}`;if(t&&t.negated===true){a=`^(?!${a}).*$`}const i=picomatch.toRegex(a,e);if(n===true){i.state=t}return i};picomatch.makeRe=(t,e={},u=false,n=false)=>{if(!t||typeof t!==\"string\"){throw new TypeError(\"Expected a non-empty string\")}let s={negated:false,fastpaths:true};if(e.fastpaths!==false&&(t[0]===\".\"||t[0]===\"*\")){s.output=o.fastpaths(t,e)}if(!s.output){s=o(t,e)}return picomatch.compileRe(s,e,u,n)};picomatch.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?\"i\":\"\"))}catch(t){if(e&&e.debug===true)throw t;return/$^/}};picomatch.constants=r;t.exports=picomatch},716:(t,e,u)=>{const n=u(96);const{CHAR_ASTERISK:o,CHAR_AT:s,CHAR_BACKWARD_SLASH:r,CHAR_COMMA:a,CHAR_DOT:i,CHAR_EXCLAMATION_MARK:c,CHAR_FORWARD_SLASH:p,CHAR_LEFT_CURLY_BRACE:l,CHAR_LEFT_PARENTHESES:f,CHAR_LEFT_SQUARE_BRACKET:A,CHAR_PLUS:_,CHAR_QUESTION_MARK:R,CHAR_RIGHT_CURLY_BRACE:E,CHAR_RIGHT_PARENTHESES:h,CHAR_RIGHT_SQUARE_BRACKET:g}=u(154);const isPathSeparator=t=>t===p||t===r;const depth=t=>{if(t.isPrefix!==true){t.depth=t.isGlobstar?Infinity:1}};const scan=(t,e)=>{const u=e||{};const b=t.length-1;const C=u.parts===true||u.scanToEnd===true;const y=[];const $=[];const x=[];let S=t;let H=-1;let v=0;let d=0;let L=false;let T=false;let O=false;let k=false;let m=false;let w=false;let N=false;let I=false;let B=false;let G=false;let D=0;let M;let P;let K={value:\"\",depth:0,isGlob:false};const eos=()=>H>=b;const peek=()=>S.charCodeAt(H+1);const advance=()=>{M=P;return S.charCodeAt(++H)};while(H<b){P=advance();let t;if(P===r){N=K.backslashes=true;P=advance();if(P===l){w=true}continue}if(w===true||P===l){D++;while(eos()!==true&&(P=advance())){if(P===r){N=K.backslashes=true;advance();continue}if(P===l){D++;continue}if(w!==true&&P===i&&(P=advance())===i){L=K.isBrace=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(w!==true&&P===a){L=K.isBrace=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===E){D--;if(D===0){w=false;L=K.isBrace=true;G=true;break}}}if(C===true){continue}break}if(P===p){y.push(H);$.push(K);K={value:\"\",depth:0,isGlob:false};if(G===true)continue;if(M===i&&H===v+1){v+=2;continue}d=H+1;continue}if(u.noext!==true){const t=P===_||P===s||P===o||P===R||P===c;if(t===true&&peek()===f){O=K.isGlob=true;k=K.isExtglob=true;G=true;if(P===c&&H===v){B=true}if(C===true){while(eos()!==true&&(P=advance())){if(P===r){N=K.backslashes=true;P=advance();continue}if(P===h){O=K.isGlob=true;G=true;break}}continue}break}}if(P===o){if(M===o)m=K.isGlobstar=true;O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===R){O=K.isGlob=true;G=true;if(C===true){continue}break}if(P===A){while(eos()!==true&&(t=advance())){if(t===r){N=K.backslashes=true;advance();continue}if(t===g){T=K.isBracket=true;O=K.isGlob=true;G=true;break}}if(C===true){continue}break}if(u.nonegate!==true&&P===c&&H===v){I=K.negated=true;v++;continue}if(u.noparen!==true&&P===f){O=K.isGlob=true;if(C===true){while(eos()!==true&&(P=advance())){if(P===f){N=K.backslashes=true;P=advance();continue}if(P===h){G=true;break}}continue}break}if(O===true){G=true;if(C===true){continue}break}}if(u.noext===true){k=false;O=false}let U=S;let X=\"\";let F=\"\";if(v>0){X=S.slice(0,v);S=S.slice(v);d-=v}if(U&&O===true&&d>0){U=S.slice(0,d);F=S.slice(d)}else if(O===true){U=\"\";F=S}else{U=S}if(U&&U!==\"\"&&U!==\"/\"&&U!==S){if(isPathSeparator(U.charCodeAt(U.length-1))){U=U.slice(0,-1)}}if(u.unescape===true){if(F)F=n.removeBackslashes(F);if(U&&N===true){U=n.removeBackslashes(U)}}const Q={prefix:X,input:t,start:v,base:U,glob:F,isBrace:L,isBracket:T,isGlob:O,isExtglob:k,isGlobstar:m,negated:I,negatedExtglob:B};if(u.tokens===true){Q.maxDepth=0;if(!isPathSeparator(P)){$.push(K)}Q.tokens=$}if(u.parts===true||u.tokens===true){let e;for(let n=0;n<y.length;n++){const o=e?e+1:v;const s=y[n];const r=t.slice(o,s);if(u.tokens){if(n===0&&v!==0){$[n].isPrefix=true;$[n].value=X}else{$[n].value=r}depth($[n]);Q.maxDepth+=$[n].depth}if(n!==0||r!==\"\"){x.push(r)}e=s}if(e&&e+1<t.length){const n=t.slice(e+1);x.push(n);if(u.tokens){$[$.length-1].value=n;depth($[$.length-1]);Q.maxDepth+=$[$.length-1].depth}}Q.slashes=y;Q.parts=x}return Q};t.exports=scan},96:(t,e,u)=>{const{REGEX_BACKSLASH:n,REGEX_REMOVE_BACKSLASH:o,REGEX_SPECIAL_CHARS:s,REGEX_SPECIAL_CHARS_GLOBAL:r}=u(154);e.isObject=t=>t!==null&&typeof t===\"object\"&&!Array.isArray(t);e.hasRegexChars=t=>s.test(t);e.isRegexChar=t=>t.length===1&&e.hasRegexChars(t);e.escapeRegex=t=>t.replace(r,\"\\\\$1\");e.toPosixSlashes=t=>t.replace(n,\"/\");e.removeBackslashes=t=>t.replace(o,(t=>t===\"\\\\\"?\"\":t));e.escapeLast=(t,u,n)=>{const o=t.lastIndexOf(u,n);if(o===-1)return t;if(t[o-1]===\"\\\\\")return e.escapeLast(t,u,o-1);return`${t.slice(0,o)}\\\\${t.slice(o)}`};e.removePrefix=(t,e={})=>{let u=t;if(u.startsWith(\"./\")){u=u.slice(2);e.prefix=\"./\"}return u};e.wrapOutput=(t,e={},u={})=>{const n=u.contains?\"\":\"^\";const o=u.contains?\"\":\"$\";let s=`${n}(?:${t})${o}`;if(e.negated===true){s=`(?:^(?!${s}).*$)`}return s};e.basename=(t,{windows:e}={})=>{const u=t.split(e?/[\\\\/]/:\"/\");const n=u[u.length-1];if(n===\"\"){return u[u.length-2]}return n}}};var e={};function __nccwpck_require__(u){var n=e[u];if(n!==undefined){return n.exports}var o=e[u]={exports:{}};var s=true;try{t[u](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete e[u]}return o.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var u=__nccwpck_require__(170);module.exports=u})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/image-component.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/client/image-component.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Image\", ({\n    enumerable: true,\n    get: function() {\n        return Image;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js\"));\nconst _getimgprops = __webpack_require__(/*! ../shared/lib/get-img-props */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\");\nconst _imageconfig = __webpack_require__(/*! ../shared/lib/image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _warnonce = __webpack_require__(/*! ../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _imageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/shared/lib/image-loader */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\"));\nconst _usemergedref = __webpack_require__(/*! ./use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\n// This is replaced by webpack define plugin\nconst configEnv = {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[]};\nif (typeof window === 'undefined') {\n    ;\n    globalThis.__NEXT_IMAGE_IMPORTED = true;\n}\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput) {\n    const src = img == null ? void 0 : img.src;\n    if (!img || img['data-loaded-src'] === src) {\n        return;\n    }\n    img['data-loaded-src'] = src;\n    const p = 'decode' in img ? img.decode() : Promise.resolve();\n    p.catch(()=>{}).then(()=>{\n        if (!img.parentElement || !img.isConnected) {\n            // Exit early in case of race condition:\n            // - onload() is called\n            // - decode() is called but incomplete\n            // - unmount is called\n            // - decode() completes\n            return;\n        }\n        if (placeholder !== 'empty') {\n            setBlurComplete(true);\n        }\n        if (onLoadRef == null ? void 0 : onLoadRef.current) {\n            // Since we don't have the SyntheticEvent here,\n            // we must create one with the same shape.\n            // See https://reactjs.org/docs/events.html\n            const event = new Event('load');\n            Object.defineProperty(event, 'target', {\n                writable: false,\n                value: img\n            });\n            let prevented = false;\n            let stopped = false;\n            onLoadRef.current({\n                ...event,\n                nativeEvent: event,\n                currentTarget: img,\n                target: img,\n                isDefaultPrevented: ()=>prevented,\n                isPropagationStopped: ()=>stopped,\n                persist: ()=>{},\n                preventDefault: ()=>{\n                    prevented = true;\n                    event.preventDefault();\n                },\n                stopPropagation: ()=>{\n                    stopped = true;\n                    event.stopPropagation();\n                }\n            });\n        }\n        if (onLoadingCompleteRef == null ? void 0 : onLoadingCompleteRef.current) {\n            onLoadingCompleteRef.current(img);\n        }\n        if (true) {\n            const origSrc = new URL(src, 'http://n').searchParams.get('url') || src;\n            if (img.getAttribute('data-nimg') === 'fill') {\n                if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n                    let widthViewportRatio = img.getBoundingClientRect().width / window.innerWidth;\n                    if (widthViewportRatio < 0.6) {\n                        if (sizesInput === '100vw') {\n                            (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes');\n                        } else {\n                            (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes');\n                        }\n                    }\n                }\n                if (img.parentElement) {\n                    const { position } = window.getComputedStyle(img.parentElement);\n                    const valid = [\n                        'absolute',\n                        'fixed',\n                        'relative'\n                    ];\n                    if (!valid.includes(position)) {\n                        (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" and parent element with invalid \"position\". Provided \"' + position + '\" should be one of ' + valid.map(String).join(',') + \".\");\n                    }\n                }\n                if (img.height === 0) {\n                    (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.');\n                }\n            }\n            const heightModified = img.height.toString() !== img.getAttribute('height');\n            const widthModified = img.width.toString() !== img.getAttribute('width');\n            if (heightModified && !widthModified || !heightModified && widthModified) {\n                (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles \\'width: \"auto\"\\' or \\'height: \"auto\"\\' to maintain the aspect ratio.');\n            }\n        }\n    });\n}\nfunction getDynamicProps(fetchPriority) {\n    if (Boolean(_react.use)) {\n        // In React 19.0.0 or newer, we must use camelCase\n        // prop to avoid \"Warning: Invalid DOM property\".\n        // See https://github.com/facebook/react/pull/25927\n        return {\n            fetchPriority\n        };\n    }\n    // In React 18.2.0 or older, we must use lowercase prop\n    // to avoid \"Warning: Invalid DOM property\".\n    return {\n        fetchpriority: fetchPriority\n    };\n}\nconst ImageElement = /*#__PURE__*/ (0, _react.forwardRef)((param, forwardedRef)=>{\n    let { src, srcSet, sizes, height, width, decoding, className, style, fetchPriority, placeholder, loading, unoptimized, fill, onLoadRef, onLoadingCompleteRef, setBlurComplete, setShowAltText, sizesInput, onLoad, onError, ...rest } = param;\n    const ownRef = (0, _react.useCallback)((img)=>{\n        if (!img) {\n            return;\n        }\n        if (onError) {\n            // If the image has an error before react hydrates, then the error is lost.\n            // The workaround is to wait until the image is mounted which is after hydration,\n            // then we set the src again to trigger the error handler (if there was an error).\n            // eslint-disable-next-line no-self-assign\n            img.src = img.src;\n        }\n        if (true) {\n            if (!src) {\n                console.error('Image is missing required \"src\" property:', img);\n            }\n            if (img.getAttribute('alt') === null) {\n                console.error('Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.');\n            }\n        }\n        if (img.complete) {\n            handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput);\n        }\n    }, [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput\n    ]);\n    const ref = (0, _usemergedref.useMergedRef)(forwardedRef, ownRef);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"img\", {\n        ...rest,\n        ...getDynamicProps(fetchPriority),\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading: loading,\n        width: width,\n        height: height,\n        decoding: decoding,\n        \"data-nimg\": fill ? 'fill' : '1',\n        className: className,\n        style: style,\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes: sizes,\n        srcSet: srcSet,\n        src: src,\n        ref: ref,\n        onLoad: (event)=>{\n            const img = event.currentTarget;\n            handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput);\n        },\n        onError: (event)=>{\n            // if the real image fails to load, this will ensure \"alt\" is visible\n            setShowAltText(true);\n            if (placeholder !== 'empty') {\n                // If the real image fails to load, this will still remove the placeholder.\n                setBlurComplete(true);\n            }\n            if (onError) {\n                onError(event);\n            }\n        }\n    });\n});\nfunction ImagePreload(param) {\n    let { isAppRouter, imgAttributes } = param;\n    const opts = {\n        as: 'image',\n        imageSrcSet: imgAttributes.srcSet,\n        imageSizes: imgAttributes.sizes,\n        crossOrigin: imgAttributes.crossOrigin,\n        referrerPolicy: imgAttributes.referrerPolicy,\n        ...getDynamicProps(imgAttributes.fetchPriority)\n    };\n    if (isAppRouter && _reactdom.default.preload) {\n        // See https://github.com/facebook/react/pull/26940\n        _reactdom.default.preload(imgAttributes.src, opts);\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"preload\",\n            // Note how we omit the `href` attribute, as it would only be relevant\n            // for browsers that do not support `imagesrcset`, and in those cases\n            // it would cause the incorrect image to be preloaded.\n            //\n            // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n            href: imgAttributes.srcSet ? undefined : imgAttributes.src,\n            ...opts\n        }, '__nimg-' + imgAttributes.src + imgAttributes.srcSet + imgAttributes.sizes)\n    });\n}\n_c = ImagePreload;\nconst Image = /*#__PURE__*/ (0, _react.forwardRef)((props, forwardedRef)=>{\n    const pagesRouter = (0, _react.useContext)(_routercontextsharedruntime.RouterContext);\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const configContext = (0, _react.useContext)(_imageconfigcontextsharedruntime.ImageConfigContext);\n    const config = (0, _react.useMemo)(()=>{\n        var _c_qualities;\n        const c = configEnv || configContext || _imageconfig.imageConfigDefault;\n        const allSizes = [\n            ...c.deviceSizes,\n            ...c.imageSizes\n        ].sort((a, b)=>a - b);\n        const deviceSizes = c.deviceSizes.sort((a, b)=>a - b);\n        const qualities = (_c_qualities = c.qualities) == null ? void 0 : _c_qualities.sort((a, b)=>a - b);\n        return {\n            ...c,\n            allSizes,\n            deviceSizes,\n            qualities\n        };\n    }, [\n        configContext\n    ]);\n    const { onLoad, onLoadingComplete } = props;\n    const onLoadRef = (0, _react.useRef)(onLoad);\n    (0, _react.useEffect)(()=>{\n        onLoadRef.current = onLoad;\n    }, [\n        onLoad\n    ]);\n    const onLoadingCompleteRef = (0, _react.useRef)(onLoadingComplete);\n    (0, _react.useEffect)(()=>{\n        onLoadingCompleteRef.current = onLoadingComplete;\n    }, [\n        onLoadingComplete\n    ]);\n    const [blurComplete, setBlurComplete] = (0, _react.useState)(false);\n    const [showAltText, setShowAltText] = (0, _react.useState)(false);\n    const { props: imgAttributes, meta: imgMeta } = (0, _getimgprops.getImgProps)(props, {\n        defaultLoader: _imageloader.default,\n        imgConf: config,\n        blurComplete,\n        showAltText\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(ImageElement, {\n                ...imgAttributes,\n                unoptimized: imgMeta.unoptimized,\n                placeholder: imgMeta.placeholder,\n                fill: imgMeta.fill,\n                onLoadRef: onLoadRef,\n                onLoadingCompleteRef: onLoadingCompleteRef,\n                setBlurComplete: setBlurComplete,\n                setShowAltText: setShowAltText,\n                sizesInput: props.sizes,\n                ref: forwardedRef\n            }),\n            imgMeta.priority ? /*#__PURE__*/ (0, _jsxruntime.jsx)(ImagePreload, {\n                isAppRouter: isAppRouter,\n                imgAttributes: imgAttributes\n            }) : null\n        ]\n    });\n});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=image-component.js.map\nvar _c;\n$RefreshReg$(_c, \"ImagePreload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/image-component.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(()=>{});\n    const cleanupB = (0, _react.useRef)(()=>{});\n    return (0, _react.useMemo)(()=>{\n        if (!refA || !refB) {\n            return refA || refB;\n        }\n        return (current)=>{\n            if (current === null) {\n                cleanupA.current();\n                cleanupB.current();\n            } else {\n                cleanupA.current = applyRef(refA, current);\n                cleanupB.current = applyRef(refB, current);\n            }\n        };\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n    AmpStateContext.displayName = 'AmpStateContext';\n} //# sourceMappingURL=amp-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEsa0JBQXNDQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRXhFLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDSCxnQkFBZ0JNLFdBQVcsR0FBRztBQUNoQyIsInNvdXJjZXMiOlsiRDpcXHNyY1xcc2hhcmVkXFxsaWJcXGFtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGNvbnN0IEFtcFN0YXRlQ29udGV4dDogUmVhY3QuQ29udGV4dDxhbnk+ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7fSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgQW1wU3RhdGVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ0FtcFN0YXRlQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJBbXBTdGF0ZUNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OytDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsWUFBWTtJQUFBLE1BQzFCQyxXQUFXLEtBQUssRUFDaEJDLFNBQVMsS0FBSyxFQUNkQyxXQUFXLEtBQUssRUFDakIsR0FKMkIsbUJBSXhCLENBQUMsSUFKdUI7SUFLMUIsT0FBT0YsWUFBYUMsVUFBVUM7QUFDaEMiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxhbXAtbW9kZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNJbkFtcE1vZGUoe1xuICBhbXBGaXJzdCA9IGZhbHNlLFxuICBoeWJyaWQgPSBmYWxzZSxcbiAgaGFzUXVlcnkgPSBmYWxzZSxcbn0gPSB7fSk6IGJvb2xlYW4ge1xuICByZXR1cm4gYW1wRmlyc3QgfHwgKGh5YnJpZCAmJiBoYXNRdWVyeSlcbn1cbiJdLCJuYW1lcyI6WyJpc0luQW1wTW9kZSIsImFtcEZpcnN0IiwiaHlicmlkIiwiaGFzUXVlcnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/get-img-props.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getImgProps\", ({\n    enumerable: true,\n    get: function() {\n        return getImgProps;\n    }\n}));\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _imageblursvg = __webpack_require__(/*! ./image-blur-svg */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js\");\nconst _imageconfig = __webpack_require__(/*! ./image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst VALID_LOADING_VALUES = [\n    'lazy',\n    'eager',\n    undefined\n];\nfunction isStaticRequire(src) {\n    return src.default !== undefined;\n}\nfunction isStaticImageData(src) {\n    return src.src !== undefined;\n}\nfunction isStaticImport(src) {\n    return !!src && typeof src === 'object' && (isStaticRequire(src) || isStaticImageData(src));\n}\nconst allImgs = new Map();\nlet perfObserver;\nfunction getInt(x) {\n    if (typeof x === 'undefined') {\n        return x;\n    }\n    if (typeof x === 'number') {\n        return Number.isFinite(x) ? x : NaN;\n    }\n    if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n        return parseInt(x, 10);\n    }\n    return NaN;\n}\nfunction getWidths(param, width, sizes) {\n    let { deviceSizes, allSizes } = param;\n    if (sizes) {\n        // Find all the \"vw\" percent sizes used in the sizes prop\n        const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g;\n        const percentSizes = [];\n        for(let match; match = viewportWidthRe.exec(sizes); match){\n            percentSizes.push(parseInt(match[2]));\n        }\n        if (percentSizes.length) {\n            const smallestRatio = Math.min(...percentSizes) * 0.01;\n            return {\n                widths: allSizes.filter((s)=>s >= deviceSizes[0] * smallestRatio),\n                kind: 'w'\n            };\n        }\n        return {\n            widths: allSizes,\n            kind: 'w'\n        };\n    }\n    if (typeof width !== 'number') {\n        return {\n            widths: deviceSizes,\n            kind: 'w'\n        };\n    }\n    const widths = [\n        ...new Set(// > are actually 3x in the green color, but only 1.5x in the red and\n        // > blue colors. Showing a 3x resolution image in the app vs a 2x\n        // > resolution image will be visually the same, though the 3x image\n        // > takes significantly more data. Even true 3x resolution screens are\n        // > wasteful as the human eye cannot see that level of detail without\n        // > something like a magnifying glass.\n        // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n        [\n            width,\n            width * 2 /*, width * 3*/ \n        ].map((w)=>allSizes.find((p)=>p >= w) || allSizes[allSizes.length - 1]))\n    ];\n    return {\n        widths,\n        kind: 'x'\n    };\n}\nfunction generateImgAttrs(param) {\n    let { config, src, unoptimized, width, quality, sizes, loader } = param;\n    if (unoptimized) {\n        return {\n            src,\n            srcSet: undefined,\n            sizes: undefined\n        };\n    }\n    const { widths, kind } = getWidths(config, width, sizes);\n    const last = widths.length - 1;\n    return {\n        sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n        srcSet: widths.map((w, i)=>loader({\n                config,\n                src,\n                quality,\n                width: w\n            }) + \" \" + (kind === 'w' ? w : i + 1) + kind).join(', '),\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        src: loader({\n            config,\n            src,\n            quality,\n            width: widths[last]\n        })\n    };\n}\nfunction getImgProps(param, _state) {\n    let { src, sizes, unoptimized = false, priority = false, loading, className, quality, width, height, fill = false, style, overrideSrc, onLoad, onLoadingComplete, placeholder = 'empty', blurDataURL, fetchPriority, decoding = 'async', layout, objectFit, objectPosition, lazyBoundary, lazyRoot, ...rest } = param;\n    const { imgConf, showAltText, blurComplete, defaultLoader } = _state;\n    let config;\n    let c = imgConf || _imageconfig.imageConfigDefault;\n    if ('allSizes' in c) {\n        config = c;\n    } else {\n        var _c_qualities;\n        const allSizes = [\n            ...c.deviceSizes,\n            ...c.imageSizes\n        ].sort((a, b)=>a - b);\n        const deviceSizes = c.deviceSizes.sort((a, b)=>a - b);\n        const qualities = (_c_qualities = c.qualities) == null ? void 0 : _c_qualities.sort((a, b)=>a - b);\n        config = {\n            ...c,\n            allSizes,\n            deviceSizes,\n            qualities\n        };\n    }\n    if (typeof defaultLoader === 'undefined') {\n        throw new Error('images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config');\n    }\n    let loader = rest.loader || defaultLoader;\n    // Remove property so it's not spread on <img> element\n    delete rest.loader;\n    delete rest.srcSet;\n    // This special value indicates that the user\n    // didn't define a \"loader\" prop or \"loader\" config.\n    const isDefaultLoader = '__next_img_default' in loader;\n    if (isDefaultLoader) {\n        if (config.loader === 'custom') {\n            throw new Error('Image with src \"' + src + '\" is missing \"loader\" prop.' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader\");\n        }\n    } else {\n        // The user defined a \"loader\" prop or config.\n        // Since the config object is internal only, we\n        // must not pass it to the user-defined \"loader\".\n        const customImageLoader = loader;\n        loader = (obj)=>{\n            const { config: _, ...opts } = obj;\n            return customImageLoader(opts);\n        };\n    }\n    if (layout) {\n        if (layout === 'fill') {\n            fill = true;\n        }\n        const layoutToStyle = {\n            intrinsic: {\n                maxWidth: '100%',\n                height: 'auto'\n            },\n            responsive: {\n                width: '100%',\n                height: 'auto'\n            }\n        };\n        const layoutToSizes = {\n            responsive: '100vw',\n            fill: '100vw'\n        };\n        const layoutStyle = layoutToStyle[layout];\n        if (layoutStyle) {\n            style = {\n                ...style,\n                ...layoutStyle\n            };\n        }\n        const layoutSizes = layoutToSizes[layout];\n        if (layoutSizes && !sizes) {\n            sizes = layoutSizes;\n        }\n    }\n    let staticSrc = '';\n    let widthInt = getInt(width);\n    let heightInt = getInt(height);\n    let blurWidth;\n    let blurHeight;\n    if (isStaticImport(src)) {\n        const staticImageData = isStaticRequire(src) ? src.default : src;\n        if (!staticImageData.src) {\n            throw new Error(\"An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received \" + JSON.stringify(staticImageData));\n        }\n        if (!staticImageData.height || !staticImageData.width) {\n            throw new Error(\"An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received \" + JSON.stringify(staticImageData));\n        }\n        blurWidth = staticImageData.blurWidth;\n        blurHeight = staticImageData.blurHeight;\n        blurDataURL = blurDataURL || staticImageData.blurDataURL;\n        staticSrc = staticImageData.src;\n        if (!fill) {\n            if (!widthInt && !heightInt) {\n                widthInt = staticImageData.width;\n                heightInt = staticImageData.height;\n            } else if (widthInt && !heightInt) {\n                const ratio = widthInt / staticImageData.width;\n                heightInt = Math.round(staticImageData.height * ratio);\n            } else if (!widthInt && heightInt) {\n                const ratio = heightInt / staticImageData.height;\n                widthInt = Math.round(staticImageData.width * ratio);\n            }\n        }\n    }\n    src = typeof src === 'string' ? src : staticSrc;\n    let isLazy = !priority && (loading === 'lazy' || typeof loading === 'undefined');\n    if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n        // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n        unoptimized = true;\n        isLazy = false;\n    }\n    if (config.unoptimized) {\n        unoptimized = true;\n    }\n    if (isDefaultLoader && !config.dangerouslyAllowSVG && src.split('?', 1)[0].endsWith('.svg')) {\n        // Special case to make svg serve as-is to avoid proxying\n        // through the built-in Image Optimization API.\n        unoptimized = true;\n    }\n    const qualityInt = getInt(quality);\n    if (true) {\n        if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n            throw new Error(\"Image Optimization using the default loader is not compatible with `{ output: 'export' }`.\\n  Possible solutions:\\n    - Remove `{ output: 'export' }` and run \\\"next start\\\" to run server mode including the Image Optimization API.\\n    - Configure `{ images: { unoptimized: true } }` in `next.config.js` to disable the Image Optimization API.\\n  Read more: https://nextjs.org/docs/messages/export-image-api\");\n        }\n        if (!src) {\n            // React doesn't show the stack trace and there's\n            // no `src` to help identify which image, so we\n            // instead console.error(ref) during mount.\n            unoptimized = true;\n        } else {\n            if (fill) {\n                if (width) {\n                    throw new Error('Image with src \"' + src + '\" has both \"width\" and \"fill\" properties. Only one should be used.');\n                }\n                if (height) {\n                    throw new Error('Image with src \"' + src + '\" has both \"height\" and \"fill\" properties. Only one should be used.');\n                }\n                if ((style == null ? void 0 : style.position) && style.position !== 'absolute') {\n                    throw new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.');\n                }\n                if ((style == null ? void 0 : style.width) && style.width !== '100%') {\n                    throw new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.');\n                }\n                if ((style == null ? void 0 : style.height) && style.height !== '100%') {\n                    throw new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.');\n                }\n            } else {\n                if (typeof widthInt === 'undefined') {\n                    throw new Error('Image with src \"' + src + '\" is missing required \"width\" property.');\n                } else if (isNaN(widthInt)) {\n                    throw new Error('Image with src \"' + src + '\" has invalid \"width\" property. Expected a numeric value in pixels but received \"' + width + '\".');\n                }\n                if (typeof heightInt === 'undefined') {\n                    throw new Error('Image with src \"' + src + '\" is missing required \"height\" property.');\n                } else if (isNaN(heightInt)) {\n                    throw new Error('Image with src \"' + src + '\" has invalid \"height\" property. Expected a numeric value in pixels but received \"' + height + '\".');\n                }\n                // eslint-disable-next-line no-control-regex\n                if (/^[\\x00-\\x20]/.test(src)) {\n                    throw new Error('Image with src \"' + src + '\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.');\n                }\n                // eslint-disable-next-line no-control-regex\n                if (/[\\x00-\\x20]$/.test(src)) {\n                    throw new Error('Image with src \"' + src + '\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.');\n                }\n            }\n        }\n        if (!VALID_LOADING_VALUES.includes(loading)) {\n            throw new Error('Image with src \"' + src + '\" has invalid \"loading\" property. Provided \"' + loading + '\" should be one of ' + VALID_LOADING_VALUES.map(String).join(',') + \".\");\n        }\n        if (priority && loading === 'lazy') {\n            throw new Error('Image with src \"' + src + '\" has both \"priority\" and \"loading=\\'lazy\\'\" properties. Only one should be used.');\n        }\n        if (placeholder !== 'empty' && placeholder !== 'blur' && !placeholder.startsWith('data:image/')) {\n            throw new Error('Image with src \"' + src + '\" has invalid \"placeholder\" property \"' + placeholder + '\".');\n        }\n        if (placeholder !== 'empty') {\n            if (widthInt && heightInt && widthInt * heightInt < 1600) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.');\n            }\n        }\n        if (placeholder === 'blur' && !blurDataURL) {\n            const VALID_BLUR_EXT = [\n                'jpeg',\n                'png',\n                'webp',\n                'avif'\n            ] // should match next-image-loader\n            ;\n            throw new Error('Image with src \"' + src + '\" has \"placeholder=\\'blur\\'\" property but is missing the \"blurDataURL\" property.\\n        Possible solutions:\\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\\n          - Change the \"src\" property to a static import with one of the supported file types: ' + VALID_BLUR_EXT.join(',') + ' (animated images not supported)\\n          - Remove the \"placeholder\" property, effectively no blur effect\\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url');\n        }\n        if ('ref' in rest) {\n            (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.');\n        }\n        if (!unoptimized && !isDefaultLoader) {\n            const urlStr = loader({\n                config,\n                src,\n                width: widthInt || 400,\n                quality: qualityInt || 75\n            });\n            let url;\n            try {\n                url = new URL(urlStr);\n            } catch (err) {}\n            if (urlStr === src || url && url.pathname === src && !url.search) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width\");\n            }\n        }\n        if (onLoadingComplete) {\n            (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.');\n        }\n        for (const [legacyKey, legacyValue] of Object.entries({\n            layout,\n            objectFit,\n            objectPosition,\n            lazyBoundary,\n            lazyRoot\n        })){\n            if (legacyValue) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" has legacy prop \"' + legacyKey + '\". Did you forget to run the codemod?' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13\");\n            }\n        }\n        if (typeof window !== 'undefined' && !perfObserver && window.PerformanceObserver) {\n            perfObserver = new PerformanceObserver((entryList)=>{\n                for (const entry of entryList.getEntries()){\n                    var _entry_element;\n                    // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n                    const imgSrc = (entry == null ? void 0 : (_entry_element = entry.element) == null ? void 0 : _entry_element.src) || '';\n                    const lcpImage = allImgs.get(imgSrc);\n                    if (lcpImage && !lcpImage.priority && lcpImage.placeholder === 'empty' && !lcpImage.src.startsWith('data:') && !lcpImage.src.startsWith('blob:')) {\n                        // https://web.dev/lcp/#measure-lcp-in-javascript\n                        (0, _warnonce.warnOnce)('Image with src \"' + lcpImage.src + '\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.' + \"\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority\");\n                    }\n                }\n            });\n            try {\n                perfObserver.observe({\n                    type: 'largest-contentful-paint',\n                    buffered: true\n                });\n            } catch (err) {\n                // Log error but don't crash the app\n                console.error(err);\n            }\n        }\n    }\n    const imgStyle = Object.assign(fill ? {\n        position: 'absolute',\n        height: '100%',\n        width: '100%',\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0,\n        objectFit,\n        objectPosition\n    } : {}, showAltText ? {} : {\n        color: 'transparent'\n    }, style);\n    const backgroundImage = !blurComplete && placeholder !== 'empty' ? placeholder === 'blur' ? 'url(\"data:image/svg+xml;charset=utf-8,' + (0, _imageblursvg.getImageBlurSvg)({\n        widthInt,\n        heightInt,\n        blurWidth,\n        blurHeight,\n        blurDataURL: blurDataURL || '',\n        objectFit: imgStyle.objectFit\n    }) + '\")' : 'url(\"' + placeholder + '\")' // assume `data:image/`\n     : null;\n    let placeholderStyle = backgroundImage ? {\n        backgroundSize: imgStyle.objectFit || 'cover',\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage\n    } : {};\n    if (true) {\n        if (placeholderStyle.backgroundImage && placeholder === 'blur' && (blurDataURL == null ? void 0 : blurDataURL.startsWith('/'))) {\n            // During `next dev`, we don't want to generate blur placeholders with webpack\n            // because it can delay starting the dev server. Instead, `next-image-loader.js`\n            // will inline a special url to lazily generate the blur placeholder at request time.\n            placeholderStyle.backgroundImage = 'url(\"' + blurDataURL + '\")';\n        }\n    }\n    const imgAttributes = generateImgAttrs({\n        config,\n        src,\n        unoptimized,\n        width: widthInt,\n        quality: qualityInt,\n        sizes,\n        loader\n    });\n    if (true) {\n        if (typeof window !== 'undefined') {\n            let fullUrl;\n            try {\n                fullUrl = new URL(imgAttributes.src);\n            } catch (e) {\n                fullUrl = new URL(imgAttributes.src, window.location.href);\n            }\n            allImgs.set(fullUrl.href, {\n                src,\n                priority,\n                placeholder\n            });\n        }\n    }\n    const props = {\n        ...rest,\n        loading: isLazy ? 'lazy' : loading,\n        fetchPriority,\n        width: widthInt,\n        height: heightInt,\n        decoding,\n        className,\n        style: {\n            ...imgStyle,\n            ...placeholderStyle\n        },\n        sizes: imgAttributes.sizes,\n        srcSet: imgAttributes.srcSet,\n        src: overrideSrc || imgAttributes.src\n    };\n    const meta = {\n        unoptimized,\n        priority,\n        placeholder,\n        fill\n    };\n    return {\n        props,\n        meta\n    };\n} //# sourceMappingURL=get-img-props.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9nZXQtaW1nLXByb3BzLmpzIiwibWFwcGluZ3MiOiI7Ozs7K0NBOE9nQkE7OztlQUFBQTs7O3NDQTlPUzswQ0FDTzt5Q0FDRztBQStFbkMsTUFBTUMsdUJBQXVCO0lBQUM7SUFBUTtJQUFTQztDQUFVO0FBa0J6RCxTQUFTQyxnQkFDUEMsR0FBb0M7SUFFcEMsT0FBUUEsSUFBc0JDLE9BQU8sS0FBS0g7QUFDNUM7QUFFQSxTQUFTSSxrQkFDUEYsR0FBb0M7SUFFcEMsT0FBUUEsSUFBd0JBLEdBQUcsS0FBS0Y7QUFDMUM7QUFFQSxTQUFTSyxlQUFlSCxHQUEwQjtJQUNoRCxPQUNFLENBQUMsQ0FBQ0EsT0FDRixPQUFPQSxRQUFRLFlBQ2RELENBQUFBLGdCQUFnQkMsUUFDZkUsa0JBQWtCRixJQUFBQSxDQUFtQjtBQUUzQztBQUVBLE1BQU1JLFVBQVUsSUFBSUM7QUFJcEIsSUFBSUM7QUFFSixTQUFTQyxPQUFPQyxDQUFVO0lBQ3hCLElBQUksT0FBT0EsTUFBTSxhQUFhO1FBQzVCLE9BQU9BO0lBQ1Q7SUFDQSxJQUFJLE9BQU9BLE1BQU0sVUFBVTtRQUN6QixPQUFPQyxPQUFPQyxRQUFRLENBQUNGLEtBQUtBLElBQUlHO0lBQ2xDO0lBQ0EsSUFBSSxPQUFPSCxNQUFNLFlBQVksV0FBV0ksSUFBSSxDQUFDSixJQUFJO1FBQy9DLE9BQU9LLFNBQVNMLEdBQUc7SUFDckI7SUFDQSxPQUFPRztBQUNUO0FBRUEsU0FBU0csVUFDUCxLQUFzQyxFQUN0Q0MsS0FBeUIsRUFDekJDLEtBQXlCO0lBRnpCLE1BQUVDLFdBQVcsRUFBRUMsUUFBUSxFQUFlLEdBQXRDO0lBSUEsSUFBSUYsT0FBTztRQUNULHlEQUF5RDtRQUN6RCxNQUFNRyxrQkFBa0I7UUFDeEIsTUFBTUMsZUFBZSxFQUFFO1FBQ3ZCLElBQUssSUFBSUMsT0FBUUEsUUFBUUYsZ0JBQWdCRyxJQUFJLENBQUNOLFFBQVNLLE1BQU87WUFDNURELGFBQWFHLElBQUksQ0FBQ1YsU0FBU1EsS0FBSyxDQUFDLEVBQUU7UUFDckM7UUFDQSxJQUFJRCxhQUFhSSxNQUFNLEVBQUU7WUFDdkIsTUFBTUMsZ0JBQWdCQyxLQUFLQyxHQUFHLElBQUlQLGdCQUFnQjtZQUNsRCxPQUFPO2dCQUNMUSxRQUFRVixTQUFTVyxNQUFNLENBQUMsQ0FBQ0MsSUFBTUEsS0FBS2IsV0FBVyxDQUFDLEVBQUUsR0FBR1E7Z0JBQ3JETSxNQUFNO1lBQ1I7UUFDRjtRQUNBLE9BQU87WUFBRUgsUUFBUVY7WUFBVWEsTUFBTTtRQUFJO0lBQ3ZDO0lBQ0EsSUFBSSxPQUFPaEIsVUFBVSxVQUFVO1FBQzdCLE9BQU87WUFBRWEsUUFBUVg7WUFBYWMsTUFBTTtRQUFJO0lBQzFDO0lBRUEsTUFBTUgsU0FBUztXQUNWLElBQUlJLElBQ0wscUVBQ3FFO1FBQ3JFLGtFQUFrRTtRQUNsRSxvRUFBb0U7UUFDcEUsdUVBQXVFO1FBQ3ZFLHNFQUFzRTtRQUN0RSx1Q0FBdUM7UUFDdkMscUlBQXFJO1FBQ3JJO1lBQUNqQjtZQUFPQSxRQUFRLEVBQUUsYUFBYTtTQUFHLENBQUNrQixHQUFHLENBQ3BDLENBQUNDLElBQU1oQixTQUFTaUIsSUFBSSxDQUFDLENBQUNDLElBQU1BLEtBQUtGLE1BQU1oQixRQUFRLENBQUNBLFNBQVNNLE1BQU0sR0FBRyxFQUFFO0tBR3pFO0lBQ0QsT0FBTztRQUFFSTtRQUFRRyxNQUFNO0lBQUk7QUFDN0I7QUFrQkEsU0FBU00saUJBQWlCLEtBUVI7SUFSUSxNQUN4QkMsTUFBTSxFQUNOdEMsR0FBRyxFQUNIdUMsV0FBVyxFQUNYeEIsS0FBSyxFQUNMeUIsT0FBTyxFQUNQeEIsS0FBSyxFQUNMeUIsTUFBTSxFQUNVLEdBUlE7SUFTeEIsSUFBSUYsYUFBYTtRQUNmLE9BQU87WUFBRXZDO1lBQUswQyxRQUFRNUM7WUFBV2tCLE9BQU9sQjtRQUFVO0lBQ3BEO0lBRUEsTUFBTSxFQUFFOEIsTUFBTSxFQUFFRyxJQUFJLEVBQUUsR0FBR2pCLFVBQVV3QixRQUFRdkIsT0FBT0M7SUFDbEQsTUFBTTJCLE9BQU9mLE9BQU9KLE1BQU0sR0FBRztJQUU3QixPQUFPO1FBQ0xSLE9BQU8sQ0FBQ0EsU0FBU2UsU0FBUyxNQUFNLFVBQVVmO1FBQzFDMEIsUUFBUWQsT0FDTEssR0FBRyxDQUNGLENBQUNDLEdBQUdVLElBQ0NILE9BQU87Z0JBQUVIO2dCQUFRdEM7Z0JBQUt3QztnQkFBU3pCLE9BQU9tQjtZQUFFLEtBQUcsTUFDNUNILENBQUFBLFNBQVMsTUFBTUcsSUFBSVUsS0FBSSxJQUN0QmIsTUFFTmMsSUFBSSxDQUFDO1FBRVIsdUVBQXVFO1FBQ3ZFLG1FQUFtRTtRQUNuRSx5RUFBeUU7UUFDekUsMEVBQTBFO1FBQzFFLDJCQUEyQjtRQUMzQixzREFBc0Q7UUFDdEQ3QyxLQUFLeUMsT0FBTztZQUFFSDtZQUFRdEM7WUFBS3dDO1lBQVN6QixPQUFPYSxNQUFNLENBQUNlLEtBQUs7UUFBQztJQUMxRDtBQUNGO0FBS08sU0FBUy9DLFlBQ2QsS0F5QmEsRUFDYmtELE1BS0M7SUEvQkQsTUFDRTlDLEdBQUcsRUFDSGdCLEtBQUssRUFDTHVCLGNBQWMsS0FBSyxFQUNuQlEsV0FBVyxLQUFLLEVBQ2hCQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVFQsT0FBTyxFQUNQekIsS0FBSyxFQUNMbUMsTUFBTSxFQUNOQyxPQUFPLEtBQUssRUFDWkMsS0FBSyxFQUNMQyxXQUFXLEVBQ1hDLE1BQU0sRUFDTkMsaUJBQWlCLEVBQ2pCQyxjQUFjLE9BQU8sRUFDckJDLFdBQVcsRUFDWEMsYUFBYSxFQUNiQyxXQUFXLE9BQU8sRUFDbEJDLE1BQU0sRUFDTkMsU0FBUyxFQUNUQyxjQUFjLEVBQ2RDLFlBQVksRUFDWkMsUUFBUSxFQUNSLEdBQUdDLE1BQ1EsR0F6QmI7SUF5Q0EsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLFdBQVcsRUFBRUMsWUFBWSxFQUFFQyxhQUFhLEVBQUUsR0FBR3ZCO0lBQzlELElBQUlSO0lBQ0osSUFBSWdDLElBQUlKLFdBQVdLLGFBQUFBLGtCQUFrQjtJQUNyQyxJQUFJLGNBQWNELEdBQUc7UUFDbkJoQyxTQUFTZ0M7SUFDWCxPQUFPO1lBR2FBO1FBRmxCLE1BQU1wRCxXQUFXO2VBQUlvRCxFQUFFckQsV0FBVztlQUFLcUQsRUFBRUUsVUFBVTtTQUFDLENBQUNDLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUN4RSxNQUFNMUQsY0FBY3FELEVBQUVyRCxXQUFXLENBQUN3RCxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7UUFDckQsTUFBTUMsWUFBQUEsQ0FBWU4sZUFBQUEsRUFBRU0sU0FBQUEsS0FBUyxnQkFBWE4sYUFBYUcsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDO1FBQ2xEckMsU0FBUztZQUFFLEdBQUdnQyxDQUFDO1lBQUVwRDtZQUFVRDtZQUFhMkQ7UUFBVTtJQUNwRDtJQUVBLElBQUksT0FBT1Asa0JBQWtCLGFBQWE7UUFDeEMsTUFBTSxJQUFJUSxNQUNSO0lBRUo7SUFDQSxJQUFJcEMsU0FBZ0N3QixLQUFLeEIsTUFBTSxJQUFJNEI7SUFFbkQsc0RBQXNEO0lBQ3RELE9BQU9KLEtBQUt4QixNQUFNO0lBQ2xCLE9BQVF3QixLQUFhdkIsTUFBTTtJQUUzQiw2Q0FBNkM7SUFDN0Msb0RBQW9EO0lBQ3BELE1BQU1vQyxrQkFBa0Isd0JBQXdCckM7SUFFaEQsSUFBSXFDLGlCQUFpQjtRQUNuQixJQUFJeEMsT0FBT0csTUFBTSxLQUFLLFVBQVU7WUFDOUIsTUFBTSxJQUFJb0MsTUFDUCxxQkFBa0I3RSxNQUFJLGdDQUNwQjtRQUVQO0lBQ0YsT0FBTztRQUNMLDhDQUE4QztRQUM5QywrQ0FBK0M7UUFDL0MsaURBQWlEO1FBQ2pELE1BQU0rRSxvQkFBb0J0QztRQUMxQkEsU0FBUyxDQUFDdUM7WUFDUixNQUFNLEVBQUUxQyxRQUFRMkMsQ0FBQyxFQUFFLEdBQUdDLE1BQU0sR0FBR0Y7WUFDL0IsT0FBT0Qsa0JBQWtCRztRQUMzQjtJQUNGO0lBRUEsSUFBSXRCLFFBQVE7UUFDVixJQUFJQSxXQUFXLFFBQVE7WUFDckJULE9BQU87UUFDVDtRQUNBLE1BQU1nQyxnQkFBb0U7WUFDeEVDLFdBQVc7Z0JBQUVDLFVBQVU7Z0JBQVFuQyxRQUFRO1lBQU87WUFDOUNvQyxZQUFZO2dCQUFFdkUsT0FBTztnQkFBUW1DLFFBQVE7WUFBTztRQUM5QztRQUNBLE1BQU1xQyxnQkFBb0Q7WUFDeERELFlBQVk7WUFDWm5DLE1BQU07UUFDUjtRQUNBLE1BQU1xQyxjQUFjTCxhQUFhLENBQUN2QixPQUFPO1FBQ3pDLElBQUk0QixhQUFhO1lBQ2ZwQyxRQUFRO2dCQUFFLEdBQUdBLEtBQUs7Z0JBQUUsR0FBR29DLFdBQVc7WUFBQztRQUNyQztRQUNBLE1BQU1DLGNBQWNGLGFBQWEsQ0FBQzNCLE9BQU87UUFDekMsSUFBSTZCLGVBQWUsQ0FBQ3pFLE9BQU87WUFDekJBLFFBQVF5RTtRQUNWO0lBQ0Y7SUFFQSxJQUFJQyxZQUFZO0lBQ2hCLElBQUlDLFdBQVdwRixPQUFPUTtJQUN0QixJQUFJNkUsWUFBWXJGLE9BQU8yQztJQUN2QixJQUFJMkM7SUFDSixJQUFJQztJQUNKLElBQUkzRixlQUFlSCxNQUFNO1FBQ3ZCLE1BQU0rRixrQkFBa0JoRyxnQkFBZ0JDLE9BQU9BLElBQUlDLE9BQU8sR0FBR0Q7UUFFN0QsSUFBSSxDQUFDK0YsZ0JBQWdCL0YsR0FBRyxFQUFFO1lBQ3hCLE1BQU0sSUFBSTZFLE1BQ1AsZ0pBQTZJbUIsS0FBS0MsU0FBUyxDQUMxSkY7UUFHTjtRQUNBLElBQUksQ0FBQ0EsZ0JBQWdCN0MsTUFBTSxJQUFJLENBQUM2QyxnQkFBZ0JoRixLQUFLLEVBQUU7WUFDckQsTUFBTSxJQUFJOEQsTUFDUCw2SkFBMEptQixLQUFLQyxTQUFTLENBQ3ZLRjtRQUdOO1FBRUFGLFlBQVlFLGdCQUFnQkYsU0FBUztRQUNyQ0MsYUFBYUMsZ0JBQWdCRCxVQUFVO1FBQ3ZDckMsY0FBY0EsZUFBZXNDLGdCQUFnQnRDLFdBQVc7UUFDeERpQyxZQUFZSyxnQkFBZ0IvRixHQUFHO1FBRS9CLElBQUksQ0FBQ21ELE1BQU07WUFDVCxJQUFJLENBQUN3QyxZQUFZLENBQUNDLFdBQVc7Z0JBQzNCRCxXQUFXSSxnQkFBZ0JoRixLQUFLO2dCQUNoQzZFLFlBQVlHLGdCQUFnQjdDLE1BQU07WUFDcEMsT0FBTyxJQUFJeUMsWUFBWSxDQUFDQyxXQUFXO2dCQUNqQyxNQUFNTSxRQUFRUCxXQUFXSSxnQkFBZ0JoRixLQUFLO2dCQUM5QzZFLFlBQVlsRSxLQUFLeUUsS0FBSyxDQUFDSixnQkFBZ0I3QyxNQUFNLEdBQUdnRDtZQUNsRCxPQUFPLElBQUksQ0FBQ1AsWUFBWUMsV0FBVztnQkFDakMsTUFBTU0sUUFBUU4sWUFBWUcsZ0JBQWdCN0MsTUFBTTtnQkFDaER5QyxXQUFXakUsS0FBS3lFLEtBQUssQ0FBQ0osZ0JBQWdCaEYsS0FBSyxHQUFHbUY7WUFDaEQ7UUFDRjtJQUNGO0lBQ0FsRyxNQUFNLE9BQU9BLFFBQVEsV0FBV0EsTUFBTTBGO0lBRXRDLElBQUlVLFNBQ0YsQ0FBQ3JELFlBQWFDLENBQUFBLFlBQVksVUFBVSxPQUFPQSxZQUFZLFlBQVU7SUFDbkUsSUFBSSxDQUFDaEQsT0FBT0EsSUFBSXFHLFVBQVUsQ0FBQyxZQUFZckcsSUFBSXFHLFVBQVUsQ0FBQyxVQUFVO1FBQzlELHVFQUF1RTtRQUN2RTlELGNBQWM7UUFDZDZELFNBQVM7SUFDWDtJQUNBLElBQUk5RCxPQUFPQyxXQUFXLEVBQUU7UUFDdEJBLGNBQWM7SUFDaEI7SUFDQSxJQUNFdUMsbUJBQ0EsQ0FBQ3hDLE9BQU9nRSxtQkFBbUIsSUFDM0J0RyxJQUFJdUcsS0FBSyxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQ0MsUUFBUSxDQUFDLFNBQzlCO1FBQ0EseURBQXlEO1FBQ3pELCtDQUErQztRQUMvQ2pFLGNBQWM7SUFDaEI7SUFFQSxNQUFNa0UsYUFBYWxHLE9BQU9pQztJQUUxQixJQUFJa0UsSUFBb0IsRUFBbUI7UUFDekMsSUFBSXBFLE9BQU91RSxNQUFNLEtBQUssWUFBWS9CLG1CQUFtQixDQUFDdkMsYUFBYTtZQUNqRSxNQUFNLElBQUlzQyxNQUNQO1FBTUw7UUFDQSxJQUFJLENBQUM3RSxLQUFLO1lBQ1IsaURBQWlEO1lBQ2pELCtDQUErQztZQUMvQywyQ0FBMkM7WUFDM0N1QyxjQUFjO1FBQ2hCLE9BQU87WUFDTCxJQUFJWSxNQUFNO2dCQUNSLElBQUlwQyxPQUFPO29CQUNULE1BQU0sSUFBSThELE1BQ1AscUJBQWtCN0UsTUFBSTtnQkFFM0I7Z0JBQ0EsSUFBSWtELFFBQVE7b0JBQ1YsTUFBTSxJQUFJMkIsTUFDUCxxQkFBa0I3RSxNQUFJO2dCQUUzQjtnQkFDQSxJQUFJb0QsQ0FBQUEsU0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsTUFBTzBELFFBQUFBLEtBQVkxRCxNQUFNMEQsUUFBUSxLQUFLLFlBQVk7b0JBQ3BELE1BQU0sSUFBSWpDLE1BQ1AscUJBQWtCN0UsTUFBSTtnQkFFM0I7Z0JBQ0EsSUFBSW9ELENBQUFBLFNBQUFBLE9BQUFBLEtBQUFBLElBQUFBLE1BQU9yQyxLQUFBQSxLQUFTcUMsTUFBTXJDLEtBQUssS0FBSyxRQUFRO29CQUMxQyxNQUFNLElBQUk4RCxNQUNQLHFCQUFrQjdFLE1BQUk7Z0JBRTNCO2dCQUNBLElBQUlvRCxDQUFBQSxTQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxNQUFPRixNQUFBQSxLQUFVRSxNQUFNRixNQUFNLEtBQUssUUFBUTtvQkFDNUMsTUFBTSxJQUFJMkIsTUFDUCxxQkFBa0I3RSxNQUFJO2dCQUUzQjtZQUNGLE9BQU87Z0JBQ0wsSUFBSSxPQUFPMkYsYUFBYSxhQUFhO29CQUNuQyxNQUFNLElBQUlkLE1BQ1AscUJBQWtCN0UsTUFBSTtnQkFFM0IsT0FBTyxJQUFJK0csTUFBTXBCLFdBQVc7b0JBQzFCLE1BQU0sSUFBSWQsTUFDUCxxQkFBa0I3RSxNQUFJLHNGQUFtRmUsUUFBTTtnQkFFcEg7Z0JBQ0EsSUFBSSxPQUFPNkUsY0FBYyxhQUFhO29CQUNwQyxNQUFNLElBQUlmLE1BQ1AscUJBQWtCN0UsTUFBSTtnQkFFM0IsT0FBTyxJQUFJK0csTUFBTW5CLFlBQVk7b0JBQzNCLE1BQU0sSUFBSWYsTUFDUCxxQkFBa0I3RSxNQUFJLHVGQUFvRmtELFNBQU87Z0JBRXRIO2dCQUNBLDRDQUE0QztnQkFDNUMsSUFBSSxlQUFldEMsSUFBSSxDQUFDWixNQUFNO29CQUM1QixNQUFNLElBQUk2RSxNQUNQLHFCQUFrQjdFLE1BQUk7Z0JBRTNCO2dCQUNBLDRDQUE0QztnQkFDNUMsSUFBSSxlQUFlWSxJQUFJLENBQUNaLE1BQU07b0JBQzVCLE1BQU0sSUFBSTZFLE1BQ1AscUJBQWtCN0UsTUFBSTtnQkFFM0I7WUFDRjtRQUNGO1FBQ0EsSUFBSSxDQUFDSCxxQkFBcUJtSCxRQUFRLENBQUNoRSxVQUFVO1lBQzNDLE1BQU0sSUFBSTZCLE1BQ1AscUJBQWtCN0UsTUFBSSxpREFBOENnRCxVQUFRLHdCQUFxQm5ELHFCQUFxQm9DLEdBQUcsQ0FDeEhnRixRQUNBcEUsSUFBSSxDQUFDLE9BQUs7UUFFaEI7UUFDQSxJQUFJRSxZQUFZQyxZQUFZLFFBQVE7WUFDbEMsTUFBTSxJQUFJNkIsTUFDUCxxQkFBa0I3RSxNQUFJO1FBRTNCO1FBQ0EsSUFDRXdELGdCQUFnQixXQUNoQkEsZ0JBQWdCLFVBQ2hCLENBQUNBLFlBQVk2QyxVQUFVLENBQUMsZ0JBQ3hCO1lBQ0EsTUFBTSxJQUFJeEIsTUFDUCxxQkFBa0I3RSxNQUFJLDJDQUF3Q3dELGNBQVk7UUFFL0U7UUFDQSxJQUFJQSxnQkFBZ0IsU0FBUztZQUMzQixJQUFJbUMsWUFBWUMsYUFBYUQsV0FBV0MsWUFBWSxNQUFNO2dCQUN4RHNCLENBQUFBLEdBQUFBLFVBQUFBLFFBQUFBLEVBQ0cscUJBQWtCbEgsTUFBSTtZQUUzQjtRQUNGO1FBQ0EsSUFBSXdELGdCQUFnQixVQUFVLENBQUNDLGFBQWE7WUFDMUMsTUFBTTBELGlCQUFpQjtnQkFBQztnQkFBUTtnQkFBTztnQkFBUTthQUFPLENBQUMsaUNBQWlDOztZQUV4RixNQUFNLElBQUl0QyxNQUNQLHFCQUFrQjdFLE1BQUksNlRBR2tFbUgsZUFBZXRFLElBQUksQ0FDeEcsT0FDQTtRQUlSO1FBQ0EsSUFBSSxTQUFTb0IsTUFBTTtZQUNqQmlELENBQUFBLEdBQUFBLFVBQUFBLFFBQUFBLEVBQ0cscUJBQWtCbEgsTUFBSTtRQUUzQjtRQUVBLElBQUksQ0FBQ3VDLGVBQWUsQ0FBQ3VDLGlCQUFpQjtZQUNwQyxNQUFNc0MsU0FBUzNFLE9BQU87Z0JBQ3BCSDtnQkFDQXRDO2dCQUNBZSxPQUFPNEUsWUFBWTtnQkFDbkJuRCxTQUFTaUUsY0FBYztZQUN6QjtZQUNBLElBQUlZO1lBQ0osSUFBSTtnQkFDRkEsTUFBTSxJQUFJQyxJQUFJRjtZQUNoQixFQUFFLE9BQU9HLEtBQUssQ0FBQztZQUNmLElBQUlILFdBQVdwSCxPQUFRcUgsT0FBT0EsSUFBSUcsUUFBUSxLQUFLeEgsT0FBTyxDQUFDcUgsSUFBSUksTUFBTSxFQUFHO2dCQUNsRVAsQ0FBQUEsR0FBQUEsVUFBQUEsUUFBQUEsRUFDRyxxQkFBa0JsSCxNQUFJLDRIQUNwQjtZQUVQO1FBQ0Y7UUFFQSxJQUFJdUQsbUJBQW1CO1lBQ3JCMkQsQ0FBQUEsR0FBQUEsVUFBQUEsUUFBQUEsRUFDRyxxQkFBa0JsSCxNQUFJO1FBRTNCO1FBRUEsS0FBSyxNQUFNLENBQUMwSCxXQUFXQyxZQUFZLElBQUlDLE9BQU9DLE9BQU8sQ0FBQztZQUNwRGpFO1lBQ0FDO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0YsR0FBSTtZQUNGLElBQUkyRCxhQUFhO2dCQUNmVCxDQUFBQSxHQUFBQSxVQUFBQSxRQUFBQSxFQUNHLHFCQUFrQmxILE1BQUksd0JBQXFCMEgsWUFBVSwwQ0FDbkQ7WUFFUDtRQUNGO1FBRUEsSUFDRSxPQUFPSSxXQUFXLGVBQ2xCLENBQUN4SCxnQkFDRHdILE9BQU9DLG1CQUFtQixFQUMxQjtZQUNBekgsZUFBZSxJQUFJeUgsb0JBQW9CLENBQUNDO2dCQUN0QyxLQUFLLE1BQU1DLFNBQVNELFVBQVVFLFVBQVUsR0FBSTt3QkFFM0JEO29CQURmLDBFQUEwRTtvQkFDMUUsTUFBTUUsU0FBU0YsVUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsQ0FBQUEsaUJBQUFBLE1BQU9HLE9BQUFBLEtBQU8sZ0JBQWRILGVBQWdCakksR0FBQUEsS0FBTztvQkFDdEMsTUFBTXFJLFdBQVdqSSxRQUFRa0ksR0FBRyxDQUFDSDtvQkFDN0IsSUFDRUUsWUFDQSxDQUFDQSxTQUFTdEYsUUFBUSxJQUNsQnNGLFNBQVM3RSxXQUFXLEtBQUssV0FDekIsQ0FBQzZFLFNBQVNySSxHQUFHLENBQUNxRyxVQUFVLENBQUMsWUFDekIsQ0FBQ2dDLFNBQVNySSxHQUFHLENBQUNxRyxVQUFVLENBQUMsVUFDekI7d0JBQ0EsaURBQWlEO3dCQUNqRGEsQ0FBQUEsR0FBQUEsVUFBQUEsUUFBQUEsRUFDRyxxQkFBa0JtQixTQUFTckksR0FBRyxHQUFDLDhIQUM3QjtvQkFFUDtnQkFDRjtZQUNGO1lBQ0EsSUFBSTtnQkFDRk0sYUFBYWlJLE9BQU8sQ0FBQztvQkFDbkJDLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7WUFDRixFQUFFLE9BQU9sQixLQUFLO2dCQUNaLG9DQUFvQztnQkFDcENtQixRQUFRQyxLQUFLLENBQUNwQjtZQUNoQjtRQUNGO0lBQ0Y7SUFDQSxNQUFNcUIsV0FBV2hCLE9BQU9pQixNQUFNLENBQzVCMUYsT0FDSTtRQUNFMkQsVUFBVTtRQUNWNUQsUUFBUTtRQUNSbkMsT0FBTztRQUNQK0gsTUFBTTtRQUNOQyxLQUFLO1FBQ0xDLE9BQU87UUFDUEMsUUFBUTtRQUNScEY7UUFDQUM7SUFDRixJQUNBLENBQUMsR0FDTEssY0FBYyxDQUFDLElBQUk7UUFBRStFLE9BQU87SUFBYyxHQUMxQzlGO0lBR0YsTUFBTStGLGtCQUNKLENBQUMvRSxnQkFBZ0JaLGdCQUFnQixVQUM3QkEsZ0JBQWdCLFNBQ2IsMkNBQXdDNEYsQ0FBQUEsR0FBQUEsY0FBQUEsZUFBQUEsRUFBZ0I7UUFDdkR6RDtRQUNBQztRQUNBQztRQUNBQztRQUNBckMsYUFBYUEsZUFBZTtRQUM1QkksV0FBVytFLFNBQVMvRSxTQUFTO0lBQy9CLEtBQUcsT0FDRixVQUFPTCxjQUFZLEtBQUksdUJBQXVCO09BQ2pEO0lBRU4sSUFBSTZGLG1CQUFtQkYsa0JBQ25CO1FBQ0VHLGdCQUFnQlYsU0FBUy9FLFNBQVMsSUFBSTtRQUN0QzBGLG9CQUFvQlgsU0FBUzlFLGNBQWMsSUFBSTtRQUMvQzBGLGtCQUFrQjtRQUNsQkw7SUFDRixJQUNBLENBQUM7SUFFTCxJQUFJekMsSUFBb0IsRUFBb0I7UUFDMUMsSUFDRTJDLGlCQUFpQkYsZUFBZSxJQUNoQzNGLGdCQUFnQixVQUNoQkMsZ0JBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFlBQWE0QyxVQUFVLENBQUMsT0FDeEI7WUFDQSw4RUFBOEU7WUFDOUUsZ0ZBQWdGO1lBQ2hGLHFGQUFxRjtZQUNyRmdELGlCQUFpQkYsZUFBZSxHQUFJLFVBQU8xRixjQUFZO1FBQ3pEO0lBQ0Y7SUFFQSxNQUFNZ0csZ0JBQWdCcEgsaUJBQWlCO1FBQ3JDQztRQUNBdEM7UUFDQXVDO1FBQ0F4QixPQUFPNEU7UUFDUG5ELFNBQVNpRTtRQUNUekY7UUFDQXlCO0lBQ0Y7SUFFQSxJQUFJaUUsSUFBb0IsRUFBbUI7UUFDekMsSUFBSSxPQUFPb0IsV0FBVyxhQUFhO1lBQ2pDLElBQUk0QjtZQUNKLElBQUk7Z0JBQ0ZBLFVBQVUsSUFBSXBDLElBQUltQyxjQUFjekosR0FBRztZQUNyQyxFQUFFLE9BQU8ySixHQUFHO2dCQUNWRCxVQUFVLElBQUlwQyxJQUFJbUMsY0FBY3pKLEdBQUcsRUFBRThILE9BQU84QixRQUFRLENBQUNDLElBQUk7WUFDM0Q7WUFDQXpKLFFBQVEwSixHQUFHLENBQUNKLFFBQVFHLElBQUksRUFBRTtnQkFBRTdKO2dCQUFLK0M7Z0JBQVVTO1lBQVk7UUFDekQ7SUFDRjtJQUVBLE1BQU11RyxRQUFrQjtRQUN0QixHQUFHOUYsSUFBSTtRQUNQakIsU0FBU29ELFNBQVMsU0FBU3BEO1FBQzNCVTtRQUNBM0MsT0FBTzRFO1FBQ1B6QyxRQUFRMEM7UUFDUmpDO1FBQ0FWO1FBQ0FHLE9BQU87WUFBRSxHQUFHd0YsUUFBUTtZQUFFLEdBQUdTLGdCQUFnQjtRQUFDO1FBQzFDckksT0FBT3lJLGNBQWN6SSxLQUFLO1FBQzFCMEIsUUFBUStHLGNBQWMvRyxNQUFNO1FBQzVCMUMsS0FBS3FELGVBQWVvRyxjQUFjekosR0FBRztJQUN2QztJQUNBLE1BQU1nSyxPQUFPO1FBQUV6SDtRQUFhUTtRQUFVUztRQUFhTDtJQUFLO0lBQ3hELE9BQU87UUFBRTRHO1FBQU9DO0lBQUs7QUFDdkIiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxnZXQtaW1nLXByb3BzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdhcm5PbmNlIH0gZnJvbSAnLi91dGlscy93YXJuLW9uY2UnXG5pbXBvcnQgeyBnZXRJbWFnZUJsdXJTdmcgfSBmcm9tICcuL2ltYWdlLWJsdXItc3ZnJ1xuaW1wb3J0IHsgaW1hZ2VDb25maWdEZWZhdWx0IH0gZnJvbSAnLi9pbWFnZS1jb25maWcnXG5pbXBvcnQgdHlwZSB7XG4gIEltYWdlQ29uZmlnQ29tcGxldGUsXG4gIEltYWdlTG9hZGVyUHJvcHMsXG4gIEltYWdlTG9hZGVyUHJvcHNXaXRoQ29uZmlnLFxufSBmcm9tICcuL2ltYWdlLWNvbmZpZydcblxuaW1wb3J0IHR5cGUgeyBKU1ggfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGludGVyZmFjZSBTdGF0aWNJbWFnZURhdGEge1xuICBzcmM6IHN0cmluZ1xuICBoZWlnaHQ6IG51bWJlclxuICB3aWR0aDogbnVtYmVyXG4gIGJsdXJEYXRhVVJMPzogc3RyaW5nXG4gIGJsdXJXaWR0aD86IG51bWJlclxuICBibHVySGVpZ2h0PzogbnVtYmVyXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3RhdGljUmVxdWlyZSB7XG4gIGRlZmF1bHQ6IFN0YXRpY0ltYWdlRGF0YVxufVxuXG5leHBvcnQgdHlwZSBTdGF0aWNJbXBvcnQgPSBTdGF0aWNSZXF1aXJlIHwgU3RhdGljSW1hZ2VEYXRhXG5cbmV4cG9ydCB0eXBlIEltYWdlUHJvcHMgPSBPbWl0PFxuICBKU1guSW50cmluc2ljRWxlbWVudHNbJ2ltZyddLFxuICAnc3JjJyB8ICdzcmNTZXQnIHwgJ3JlZicgfCAnYWx0JyB8ICd3aWR0aCcgfCAnaGVpZ2h0JyB8ICdsb2FkaW5nJ1xuPiAmIHtcbiAgc3JjOiBzdHJpbmcgfCBTdGF0aWNJbXBvcnRcbiAgYWx0OiBzdHJpbmdcbiAgd2lkdGg/OiBudW1iZXIgfCBgJHtudW1iZXJ9YFxuICBoZWlnaHQ/OiBudW1iZXIgfCBgJHtudW1iZXJ9YFxuICBmaWxsPzogYm9vbGVhblxuICBsb2FkZXI/OiBJbWFnZUxvYWRlclxuICBxdWFsaXR5PzogbnVtYmVyIHwgYCR7bnVtYmVyfWBcbiAgcHJpb3JpdHk/OiBib29sZWFuXG4gIGxvYWRpbmc/OiBMb2FkaW5nVmFsdWVcbiAgcGxhY2Vob2xkZXI/OiBQbGFjZWhvbGRlclZhbHVlXG4gIGJsdXJEYXRhVVJMPzogc3RyaW5nXG4gIHVub3B0aW1pemVkPzogYm9vbGVhblxuICBvdmVycmlkZVNyYz86IHN0cmluZ1xuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBvbkxvYWRgIGluc3RlYWQuXG4gICAqIEBzZWUgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvY29tcG9uZW50cy9pbWFnZSNvbmxvYWRcbiAgICovXG4gIG9uTG9hZGluZ0NvbXBsZXRlPzogT25Mb2FkaW5nQ29tcGxldGVcbiAgLyoqXG4gICAqIEBkZXByZWNhdGVkIFVzZSBgZmlsbGAgcHJvcCBpbnN0ZWFkIG9mIGBsYXlvdXQ9XCJmaWxsXCJgIG9yIGNoYW5nZSBpbXBvcnQgdG8gYG5leHQvbGVnYWN5L2ltYWdlYC5cbiAgICogQHNlZSBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcGktcmVmZXJlbmNlL25leHQvbGVnYWN5L2ltYWdlXG4gICAqL1xuICBsYXlvdXQ/OiBzdHJpbmdcbiAgLyoqXG4gICAqIEBkZXByZWNhdGVkIFVzZSBgc3R5bGVgIHByb3AgaW5zdGVhZC5cbiAgICovXG4gIG9iamVjdEZpdD86IHN0cmluZ1xuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgVXNlIGBzdHlsZWAgcHJvcCBpbnN0ZWFkLlxuICAgKi9cbiAgb2JqZWN0UG9zaXRpb24/OiBzdHJpbmdcbiAgLyoqXG4gICAqIEBkZXByZWNhdGVkIFRoaXMgcHJvcCBkb2VzIG5vdCBkbyBhbnl0aGluZy5cbiAgICovXG4gIGxhenlCb3VuZGFyeT86IHN0cmluZ1xuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgVGhpcyBwcm9wIGRvZXMgbm90IGRvIGFueXRoaW5nLlxuICAgKi9cbiAgbGF6eVJvb3Q/OiBzdHJpbmdcbn1cblxuZXhwb3J0IHR5cGUgSW1nUHJvcHMgPSBPbWl0PEltYWdlUHJvcHMsICdzcmMnIHwgJ2xvYWRlcic+ICYge1xuICBsb2FkaW5nOiBMb2FkaW5nVmFsdWVcbiAgd2lkdGg6IG51bWJlciB8IHVuZGVmaW5lZFxuICBoZWlnaHQ6IG51bWJlciB8IHVuZGVmaW5lZFxuICBzdHlsZTogTm9uTnVsbGFibGU8SlNYLkludHJpbnNpY0VsZW1lbnRzWydpbWcnXVsnc3R5bGUnXT5cbiAgc2l6ZXM6IHN0cmluZyB8IHVuZGVmaW5lZFxuICBzcmNTZXQ6IHN0cmluZyB8IHVuZGVmaW5lZFxuICBzcmM6IHN0cmluZ1xufVxuXG5jb25zdCBWQUxJRF9MT0FESU5HX1ZBTFVFUyA9IFsnbGF6eScsICdlYWdlcicsIHVuZGVmaW5lZF0gYXMgY29uc3RcbnR5cGUgTG9hZGluZ1ZhbHVlID0gKHR5cGVvZiBWQUxJRF9MT0FESU5HX1ZBTFVFUylbbnVtYmVyXVxudHlwZSBJbWFnZUNvbmZpZyA9IEltYWdlQ29uZmlnQ29tcGxldGUgJiB7XG4gIGFsbFNpemVzOiBudW1iZXJbXVxuICBvdXRwdXQ/OiAnc3RhbmRhbG9uZScgfCAnZXhwb3J0J1xufVxuXG5leHBvcnQgdHlwZSBJbWFnZUxvYWRlciA9IChwOiBJbWFnZUxvYWRlclByb3BzKSA9PiBzdHJpbmdcblxuLy8gRG8gbm90IGV4cG9ydCAtIHRoaXMgaXMgYW4gaW50ZXJuYWwgdHlwZSBvbmx5XG4vLyBiZWNhdXNlIGBuZXh0LmNvbmZpZy5qc2AgaXMgb25seSBtZWFudCBmb3IgdGhlXG4vLyBidWlsdC1pbiBsb2FkZXJzLCBub3QgZm9yIGEgY3VzdG9tIGxvYWRlcigpIHByb3AuXG50eXBlIEltYWdlTG9hZGVyV2l0aENvbmZpZyA9IChwOiBJbWFnZUxvYWRlclByb3BzV2l0aENvbmZpZykgPT4gc3RyaW5nXG5cbmV4cG9ydCB0eXBlIFBsYWNlaG9sZGVyVmFsdWUgPSAnYmx1cicgfCAnZW1wdHknIHwgYGRhdGE6aW1hZ2UvJHtzdHJpbmd9YFxuZXhwb3J0IHR5cGUgT25Mb2FkID0gUmVhY3QuUmVhY3RFdmVudEhhbmRsZXI8SFRNTEltYWdlRWxlbWVudD4gfCB1bmRlZmluZWRcbmV4cG9ydCB0eXBlIE9uTG9hZGluZ0NvbXBsZXRlID0gKGltZzogSFRNTEltYWdlRWxlbWVudCkgPT4gdm9pZFxuXG5mdW5jdGlvbiBpc1N0YXRpY1JlcXVpcmUoXG4gIHNyYzogU3RhdGljUmVxdWlyZSB8IFN0YXRpY0ltYWdlRGF0YVxuKTogc3JjIGlzIFN0YXRpY1JlcXVpcmUge1xuICByZXR1cm4gKHNyYyBhcyBTdGF0aWNSZXF1aXJlKS5kZWZhdWx0ICE9PSB1bmRlZmluZWRcbn1cblxuZnVuY3Rpb24gaXNTdGF0aWNJbWFnZURhdGEoXG4gIHNyYzogU3RhdGljUmVxdWlyZSB8IFN0YXRpY0ltYWdlRGF0YVxuKTogc3JjIGlzIFN0YXRpY0ltYWdlRGF0YSB7XG4gIHJldHVybiAoc3JjIGFzIFN0YXRpY0ltYWdlRGF0YSkuc3JjICE9PSB1bmRlZmluZWRcbn1cblxuZnVuY3Rpb24gaXNTdGF0aWNJbXBvcnQoc3JjOiBzdHJpbmcgfCBTdGF0aWNJbXBvcnQpOiBzcmMgaXMgU3RhdGljSW1wb3J0IHtcbiAgcmV0dXJuIChcbiAgICAhIXNyYyAmJlxuICAgIHR5cGVvZiBzcmMgPT09ICdvYmplY3QnICYmXG4gICAgKGlzU3RhdGljUmVxdWlyZShzcmMgYXMgU3RhdGljSW1wb3J0KSB8fFxuICAgICAgaXNTdGF0aWNJbWFnZURhdGEoc3JjIGFzIFN0YXRpY0ltcG9ydCkpXG4gIClcbn1cblxuY29uc3QgYWxsSW1ncyA9IG5ldyBNYXA8XG4gIHN0cmluZyxcbiAgeyBzcmM6IHN0cmluZzsgcHJpb3JpdHk6IGJvb2xlYW47IHBsYWNlaG9sZGVyOiBQbGFjZWhvbGRlclZhbHVlIH1cbj4oKVxubGV0IHBlcmZPYnNlcnZlcjogUGVyZm9ybWFuY2VPYnNlcnZlciB8IHVuZGVmaW5lZFxuXG5mdW5jdGlvbiBnZXRJbnQoeDogdW5rbm93bik6IG51bWJlciB8IHVuZGVmaW5lZCB7XG4gIGlmICh0eXBlb2YgeCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4geFxuICB9XG4gIGlmICh0eXBlb2YgeCA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gTnVtYmVyLmlzRmluaXRlKHgpID8geCA6IE5hTlxuICB9XG4gIGlmICh0eXBlb2YgeCA9PT0gJ3N0cmluZycgJiYgL15bMC05XSskLy50ZXN0KHgpKSB7XG4gICAgcmV0dXJuIHBhcnNlSW50KHgsIDEwKVxuICB9XG4gIHJldHVybiBOYU5cbn1cblxuZnVuY3Rpb24gZ2V0V2lkdGhzKFxuICB7IGRldmljZVNpemVzLCBhbGxTaXplcyB9OiBJbWFnZUNvbmZpZyxcbiAgd2lkdGg6IG51bWJlciB8IHVuZGVmaW5lZCxcbiAgc2l6ZXM6IHN0cmluZyB8IHVuZGVmaW5lZFxuKTogeyB3aWR0aHM6IG51bWJlcltdOyBraW5kOiAndycgfCAneCcgfSB7XG4gIGlmIChzaXplcykge1xuICAgIC8vIEZpbmQgYWxsIHRoZSBcInZ3XCIgcGVyY2VudCBzaXplcyB1c2VkIGluIHRoZSBzaXplcyBwcm9wXG4gICAgY29uc3Qgdmlld3BvcnRXaWR0aFJlID0gLyhefFxccykoMT9cXGQ/XFxkKXZ3L2dcbiAgICBjb25zdCBwZXJjZW50U2l6ZXMgPSBbXVxuICAgIGZvciAobGV0IG1hdGNoOyAobWF0Y2ggPSB2aWV3cG9ydFdpZHRoUmUuZXhlYyhzaXplcykpOyBtYXRjaCkge1xuICAgICAgcGVyY2VudFNpemVzLnB1c2gocGFyc2VJbnQobWF0Y2hbMl0pKVxuICAgIH1cbiAgICBpZiAocGVyY2VudFNpemVzLmxlbmd0aCkge1xuICAgICAgY29uc3Qgc21hbGxlc3RSYXRpbyA9IE1hdGgubWluKC4uLnBlcmNlbnRTaXplcykgKiAwLjAxXG4gICAgICByZXR1cm4ge1xuICAgICAgICB3aWR0aHM6IGFsbFNpemVzLmZpbHRlcigocykgPT4gcyA+PSBkZXZpY2VTaXplc1swXSAqIHNtYWxsZXN0UmF0aW8pLFxuICAgICAgICBraW5kOiAndycsXG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB7IHdpZHRoczogYWxsU2l6ZXMsIGtpbmQ6ICd3JyB9XG4gIH1cbiAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4geyB3aWR0aHM6IGRldmljZVNpemVzLCBraW5kOiAndycgfVxuICB9XG5cbiAgY29uc3Qgd2lkdGhzID0gW1xuICAgIC4uLm5ldyBTZXQoXG4gICAgICAvLyA+IFRoaXMgbWVhbnMgdGhhdCBtb3N0IE9MRUQgc2NyZWVucyB0aGF0IHNheSB0aGV5IGFyZSAzeCByZXNvbHV0aW9uLFxuICAgICAgLy8gPiBhcmUgYWN0dWFsbHkgM3ggaW4gdGhlIGdyZWVuIGNvbG9yLCBidXQgb25seSAxLjV4IGluIHRoZSByZWQgYW5kXG4gICAgICAvLyA+IGJsdWUgY29sb3JzLiBTaG93aW5nIGEgM3ggcmVzb2x1dGlvbiBpbWFnZSBpbiB0aGUgYXBwIHZzIGEgMnhcbiAgICAgIC8vID4gcmVzb2x1dGlvbiBpbWFnZSB3aWxsIGJlIHZpc3VhbGx5IHRoZSBzYW1lLCB0aG91Z2ggdGhlIDN4IGltYWdlXG4gICAgICAvLyA+IHRha2VzIHNpZ25pZmljYW50bHkgbW9yZSBkYXRhLiBFdmVuIHRydWUgM3ggcmVzb2x1dGlvbiBzY3JlZW5zIGFyZVxuICAgICAgLy8gPiB3YXN0ZWZ1bCBhcyB0aGUgaHVtYW4gZXllIGNhbm5vdCBzZWUgdGhhdCBsZXZlbCBvZiBkZXRhaWwgd2l0aG91dFxuICAgICAgLy8gPiBzb21ldGhpbmcgbGlrZSBhIG1hZ25pZnlpbmcgZ2xhc3MuXG4gICAgICAvLyBodHRwczovL2Jsb2cudHdpdHRlci5jb20vZW5naW5lZXJpbmcvZW5fdXMvdG9waWNzL2luZnJhc3RydWN0dXJlLzIwMTkvY2FwcGluZy1pbWFnZS1maWRlbGl0eS1vbi11bHRyYS1oaWdoLXJlc29sdXRpb24tZGV2aWNlcy5odG1sXG4gICAgICBbd2lkdGgsIHdpZHRoICogMiAvKiwgd2lkdGggKiAzKi9dLm1hcChcbiAgICAgICAgKHcpID0+IGFsbFNpemVzLmZpbmQoKHApID0+IHAgPj0gdykgfHwgYWxsU2l6ZXNbYWxsU2l6ZXMubGVuZ3RoIC0gMV1cbiAgICAgIClcbiAgICApLFxuICBdXG4gIHJldHVybiB7IHdpZHRocywga2luZDogJ3gnIH1cbn1cblxudHlwZSBHZW5JbWdBdHRyc0RhdGEgPSB7XG4gIGNvbmZpZzogSW1hZ2VDb25maWdcbiAgc3JjOiBzdHJpbmdcbiAgdW5vcHRpbWl6ZWQ6IGJvb2xlYW5cbiAgbG9hZGVyOiBJbWFnZUxvYWRlcldpdGhDb25maWdcbiAgd2lkdGg/OiBudW1iZXJcbiAgcXVhbGl0eT86IG51bWJlclxuICBzaXplcz86IHN0cmluZ1xufVxuXG50eXBlIEdlbkltZ0F0dHJzUmVzdWx0ID0ge1xuICBzcmM6IHN0cmluZ1xuICBzcmNTZXQ6IHN0cmluZyB8IHVuZGVmaW5lZFxuICBzaXplczogc3RyaW5nIHwgdW5kZWZpbmVkXG59XG5cbmZ1bmN0aW9uIGdlbmVyYXRlSW1nQXR0cnMoe1xuICBjb25maWcsXG4gIHNyYyxcbiAgdW5vcHRpbWl6ZWQsXG4gIHdpZHRoLFxuICBxdWFsaXR5LFxuICBzaXplcyxcbiAgbG9hZGVyLFxufTogR2VuSW1nQXR0cnNEYXRhKTogR2VuSW1nQXR0cnNSZXN1bHQge1xuICBpZiAodW5vcHRpbWl6ZWQpIHtcbiAgICByZXR1cm4geyBzcmMsIHNyY1NldDogdW5kZWZpbmVkLCBzaXplczogdW5kZWZpbmVkIH1cbiAgfVxuXG4gIGNvbnN0IHsgd2lkdGhzLCBraW5kIH0gPSBnZXRXaWR0aHMoY29uZmlnLCB3aWR0aCwgc2l6ZXMpXG4gIGNvbnN0IGxhc3QgPSB3aWR0aHMubGVuZ3RoIC0gMVxuXG4gIHJldHVybiB7XG4gICAgc2l6ZXM6ICFzaXplcyAmJiBraW5kID09PSAndycgPyAnMTAwdncnIDogc2l6ZXMsXG4gICAgc3JjU2V0OiB3aWR0aHNcbiAgICAgIC5tYXAoXG4gICAgICAgICh3LCBpKSA9PlxuICAgICAgICAgIGAke2xvYWRlcih7IGNvbmZpZywgc3JjLCBxdWFsaXR5LCB3aWR0aDogdyB9KX0gJHtcbiAgICAgICAgICAgIGtpbmQgPT09ICd3JyA/IHcgOiBpICsgMVxuICAgICAgICAgIH0ke2tpbmR9YFxuICAgICAgKVxuICAgICAgLmpvaW4oJywgJyksXG5cbiAgICAvLyBJdCdzIGludGVuZGVkIHRvIGtlZXAgYHNyY2AgdGhlIGxhc3QgYXR0cmlidXRlIGJlY2F1c2UgUmVhY3QgdXBkYXRlc1xuICAgIC8vIGF0dHJpYnV0ZXMgaW4gb3JkZXIuIElmIHdlIGtlZXAgYHNyY2AgdGhlIGZpcnN0IG9uZSwgU2FmYXJpIHdpbGxcbiAgICAvLyBpbW1lZGlhdGVseSBzdGFydCB0byBmZXRjaCBgc3JjYCwgYmVmb3JlIGBzaXplc2AgYW5kIGBzcmNTZXRgIGFyZSBldmVuXG4gICAgLy8gdXBkYXRlZCBieSBSZWFjdC4gVGhhdCBjYXVzZXMgbXVsdGlwbGUgdW5uZWNlc3NhcnkgcmVxdWVzdHMgaWYgYHNyY1NldGBcbiAgICAvLyBhbmQgYHNpemVzYCBhcmUgZGVmaW5lZC5cbiAgICAvLyBUaGlzIGJ1ZyBjYW5ub3QgYmUgcmVwcm9kdWNlZCBpbiBDaHJvbWUgb3IgRmlyZWZveC5cbiAgICBzcmM6IGxvYWRlcih7IGNvbmZpZywgc3JjLCBxdWFsaXR5LCB3aWR0aDogd2lkdGhzW2xhc3RdIH0pLFxuICB9XG59XG5cbi8qKlxuICogQSBzaGFyZWQgZnVuY3Rpb24sIHVzZWQgb24gYm90aCBjbGllbnQgYW5kIHNlcnZlciwgdG8gZ2VuZXJhdGUgdGhlIHByb3BzIGZvciA8aW1nPi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEltZ1Byb3BzKFxuICB7XG4gICAgc3JjLFxuICAgIHNpemVzLFxuICAgIHVub3B0aW1pemVkID0gZmFsc2UsXG4gICAgcHJpb3JpdHkgPSBmYWxzZSxcbiAgICBsb2FkaW5nLFxuICAgIGNsYXNzTmFtZSxcbiAgICBxdWFsaXR5LFxuICAgIHdpZHRoLFxuICAgIGhlaWdodCxcbiAgICBmaWxsID0gZmFsc2UsXG4gICAgc3R5bGUsXG4gICAgb3ZlcnJpZGVTcmMsXG4gICAgb25Mb2FkLFxuICAgIG9uTG9hZGluZ0NvbXBsZXRlLFxuICAgIHBsYWNlaG9sZGVyID0gJ2VtcHR5JyxcbiAgICBibHVyRGF0YVVSTCxcbiAgICBmZXRjaFByaW9yaXR5LFxuICAgIGRlY29kaW5nID0gJ2FzeW5jJyxcbiAgICBsYXlvdXQsXG4gICAgb2JqZWN0Rml0LFxuICAgIG9iamVjdFBvc2l0aW9uLFxuICAgIGxhenlCb3VuZGFyeSxcbiAgICBsYXp5Um9vdCxcbiAgICAuLi5yZXN0XG4gIH06IEltYWdlUHJvcHMsXG4gIF9zdGF0ZToge1xuICAgIGRlZmF1bHRMb2FkZXI6IEltYWdlTG9hZGVyV2l0aENvbmZpZ1xuICAgIGltZ0NvbmY6IEltYWdlQ29uZmlnQ29tcGxldGVcbiAgICBzaG93QWx0VGV4dD86IGJvb2xlYW5cbiAgICBibHVyQ29tcGxldGU/OiBib29sZWFuXG4gIH1cbik6IHtcbiAgcHJvcHM6IEltZ1Byb3BzXG4gIG1ldGE6IHtcbiAgICB1bm9wdGltaXplZDogYm9vbGVhblxuICAgIHByaW9yaXR5OiBib29sZWFuXG4gICAgcGxhY2Vob2xkZXI6IE5vbk51bGxhYmxlPEltYWdlUHJvcHNbJ3BsYWNlaG9sZGVyJ10+XG4gICAgZmlsbDogYm9vbGVhblxuICB9XG59IHtcbiAgY29uc3QgeyBpbWdDb25mLCBzaG93QWx0VGV4dCwgYmx1ckNvbXBsZXRlLCBkZWZhdWx0TG9hZGVyIH0gPSBfc3RhdGVcbiAgbGV0IGNvbmZpZzogSW1hZ2VDb25maWdcbiAgbGV0IGMgPSBpbWdDb25mIHx8IGltYWdlQ29uZmlnRGVmYXVsdFxuICBpZiAoJ2FsbFNpemVzJyBpbiBjKSB7XG4gICAgY29uZmlnID0gYyBhcyBJbWFnZUNvbmZpZ1xuICB9IGVsc2Uge1xuICAgIGNvbnN0IGFsbFNpemVzID0gWy4uLmMuZGV2aWNlU2l6ZXMsIC4uLmMuaW1hZ2VTaXplc10uc29ydCgoYSwgYikgPT4gYSAtIGIpXG4gICAgY29uc3QgZGV2aWNlU2l6ZXMgPSBjLmRldmljZVNpemVzLnNvcnQoKGEsIGIpID0+IGEgLSBiKVxuICAgIGNvbnN0IHF1YWxpdGllcyA9IGMucXVhbGl0aWVzPy5zb3J0KChhLCBiKSA9PiBhIC0gYilcbiAgICBjb25maWcgPSB7IC4uLmMsIGFsbFNpemVzLCBkZXZpY2VTaXplcywgcXVhbGl0aWVzIH1cbiAgfVxuXG4gIGlmICh0eXBlb2YgZGVmYXVsdExvYWRlciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnaW1hZ2VzLmxvYWRlckZpbGUgZGV0ZWN0ZWQgYnV0IHRoZSBmaWxlIGlzIG1pc3NpbmcgZGVmYXVsdCBleHBvcnQuXFxuUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9pbnZhbGlkLWltYWdlcy1jb25maWcnXG4gICAgKVxuICB9XG4gIGxldCBsb2FkZXI6IEltYWdlTG9hZGVyV2l0aENvbmZpZyA9IHJlc3QubG9hZGVyIHx8IGRlZmF1bHRMb2FkZXJcblxuICAvLyBSZW1vdmUgcHJvcGVydHkgc28gaXQncyBub3Qgc3ByZWFkIG9uIDxpbWc+IGVsZW1lbnRcbiAgZGVsZXRlIHJlc3QubG9hZGVyXG4gIGRlbGV0ZSAocmVzdCBhcyBhbnkpLnNyY1NldFxuXG4gIC8vIFRoaXMgc3BlY2lhbCB2YWx1ZSBpbmRpY2F0ZXMgdGhhdCB0aGUgdXNlclxuICAvLyBkaWRuJ3QgZGVmaW5lIGEgXCJsb2FkZXJcIiBwcm9wIG9yIFwibG9hZGVyXCIgY29uZmlnLlxuICBjb25zdCBpc0RlZmF1bHRMb2FkZXIgPSAnX19uZXh0X2ltZ19kZWZhdWx0JyBpbiBsb2FkZXJcblxuICBpZiAoaXNEZWZhdWx0TG9hZGVyKSB7XG4gICAgaWYgKGNvbmZpZy5sb2FkZXIgPT09ICdjdXN0b20nKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIG1pc3NpbmcgXCJsb2FkZXJcIiBwcm9wLmAgK1xuICAgICAgICAgIGBcXG5SZWFkIG1vcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL25leHQtaW1hZ2UtbWlzc2luZy1sb2FkZXJgXG4gICAgICApXG4gICAgfVxuICB9IGVsc2Uge1xuICAgIC8vIFRoZSB1c2VyIGRlZmluZWQgYSBcImxvYWRlclwiIHByb3Agb3IgY29uZmlnLlxuICAgIC8vIFNpbmNlIHRoZSBjb25maWcgb2JqZWN0IGlzIGludGVybmFsIG9ubHksIHdlXG4gICAgLy8gbXVzdCBub3QgcGFzcyBpdCB0byB0aGUgdXNlci1kZWZpbmVkIFwibG9hZGVyXCIuXG4gICAgY29uc3QgY3VzdG9tSW1hZ2VMb2FkZXIgPSBsb2FkZXIgYXMgSW1hZ2VMb2FkZXJcbiAgICBsb2FkZXIgPSAob2JqKSA9PiB7XG4gICAgICBjb25zdCB7IGNvbmZpZzogXywgLi4ub3B0cyB9ID0gb2JqXG4gICAgICByZXR1cm4gY3VzdG9tSW1hZ2VMb2FkZXIob3B0cylcbiAgICB9XG4gIH1cblxuICBpZiAobGF5b3V0KSB7XG4gICAgaWYgKGxheW91dCA9PT0gJ2ZpbGwnKSB7XG4gICAgICBmaWxsID0gdHJ1ZVxuICAgIH1cbiAgICBjb25zdCBsYXlvdXRUb1N0eWxlOiBSZWNvcmQ8c3RyaW5nLCBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHwgdW5kZWZpbmVkPiA9IHtcbiAgICAgIGludHJpbnNpYzogeyBtYXhXaWR0aDogJzEwMCUnLCBoZWlnaHQ6ICdhdXRvJyB9LFxuICAgICAgcmVzcG9uc2l2ZTogeyB3aWR0aDogJzEwMCUnLCBoZWlnaHQ6ICdhdXRvJyB9LFxuICAgIH1cbiAgICBjb25zdCBsYXlvdXRUb1NpemVzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCB1bmRlZmluZWQ+ID0ge1xuICAgICAgcmVzcG9uc2l2ZTogJzEwMHZ3JyxcbiAgICAgIGZpbGw6ICcxMDB2dycsXG4gICAgfVxuICAgIGNvbnN0IGxheW91dFN0eWxlID0gbGF5b3V0VG9TdHlsZVtsYXlvdXRdXG4gICAgaWYgKGxheW91dFN0eWxlKSB7XG4gICAgICBzdHlsZSA9IHsgLi4uc3R5bGUsIC4uLmxheW91dFN0eWxlIH1cbiAgICB9XG4gICAgY29uc3QgbGF5b3V0U2l6ZXMgPSBsYXlvdXRUb1NpemVzW2xheW91dF1cbiAgICBpZiAobGF5b3V0U2l6ZXMgJiYgIXNpemVzKSB7XG4gICAgICBzaXplcyA9IGxheW91dFNpemVzXG4gICAgfVxuICB9XG5cbiAgbGV0IHN0YXRpY1NyYyA9ICcnXG4gIGxldCB3aWR0aEludCA9IGdldEludCh3aWR0aClcbiAgbGV0IGhlaWdodEludCA9IGdldEludChoZWlnaHQpXG4gIGxldCBibHVyV2lkdGg6IG51bWJlciB8IHVuZGVmaW5lZFxuICBsZXQgYmx1ckhlaWdodDogbnVtYmVyIHwgdW5kZWZpbmVkXG4gIGlmIChpc1N0YXRpY0ltcG9ydChzcmMpKSB7XG4gICAgY29uc3Qgc3RhdGljSW1hZ2VEYXRhID0gaXNTdGF0aWNSZXF1aXJlKHNyYykgPyBzcmMuZGVmYXVsdCA6IHNyY1xuXG4gICAgaWYgKCFzdGF0aWNJbWFnZURhdGEuc3JjKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBBbiBvYmplY3Qgc2hvdWxkIG9ubHkgYmUgcGFzc2VkIHRvIHRoZSBpbWFnZSBjb21wb25lbnQgc3JjIHBhcmFtZXRlciBpZiBpdCBjb21lcyBmcm9tIGEgc3RhdGljIGltYWdlIGltcG9ydC4gSXQgbXVzdCBpbmNsdWRlIHNyYy4gUmVjZWl2ZWQgJHtKU09OLnN0cmluZ2lmeShcbiAgICAgICAgICBzdGF0aWNJbWFnZURhdGFcbiAgICAgICAgKX1gXG4gICAgICApXG4gICAgfVxuICAgIGlmICghc3RhdGljSW1hZ2VEYXRhLmhlaWdodCB8fCAhc3RhdGljSW1hZ2VEYXRhLndpZHRoKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBBbiBvYmplY3Qgc2hvdWxkIG9ubHkgYmUgcGFzc2VkIHRvIHRoZSBpbWFnZSBjb21wb25lbnQgc3JjIHBhcmFtZXRlciBpZiBpdCBjb21lcyBmcm9tIGEgc3RhdGljIGltYWdlIGltcG9ydC4gSXQgbXVzdCBpbmNsdWRlIGhlaWdodCBhbmQgd2lkdGguIFJlY2VpdmVkICR7SlNPTi5zdHJpbmdpZnkoXG4gICAgICAgICAgc3RhdGljSW1hZ2VEYXRhXG4gICAgICAgICl9YFxuICAgICAgKVxuICAgIH1cblxuICAgIGJsdXJXaWR0aCA9IHN0YXRpY0ltYWdlRGF0YS5ibHVyV2lkdGhcbiAgICBibHVySGVpZ2h0ID0gc3RhdGljSW1hZ2VEYXRhLmJsdXJIZWlnaHRcbiAgICBibHVyRGF0YVVSTCA9IGJsdXJEYXRhVVJMIHx8IHN0YXRpY0ltYWdlRGF0YS5ibHVyRGF0YVVSTFxuICAgIHN0YXRpY1NyYyA9IHN0YXRpY0ltYWdlRGF0YS5zcmNcblxuICAgIGlmICghZmlsbCkge1xuICAgICAgaWYgKCF3aWR0aEludCAmJiAhaGVpZ2h0SW50KSB7XG4gICAgICAgIHdpZHRoSW50ID0gc3RhdGljSW1hZ2VEYXRhLndpZHRoXG4gICAgICAgIGhlaWdodEludCA9IHN0YXRpY0ltYWdlRGF0YS5oZWlnaHRcbiAgICAgIH0gZWxzZSBpZiAod2lkdGhJbnQgJiYgIWhlaWdodEludCkge1xuICAgICAgICBjb25zdCByYXRpbyA9IHdpZHRoSW50IC8gc3RhdGljSW1hZ2VEYXRhLndpZHRoXG4gICAgICAgIGhlaWdodEludCA9IE1hdGgucm91bmQoc3RhdGljSW1hZ2VEYXRhLmhlaWdodCAqIHJhdGlvKVxuICAgICAgfSBlbHNlIGlmICghd2lkdGhJbnQgJiYgaGVpZ2h0SW50KSB7XG4gICAgICAgIGNvbnN0IHJhdGlvID0gaGVpZ2h0SW50IC8gc3RhdGljSW1hZ2VEYXRhLmhlaWdodFxuICAgICAgICB3aWR0aEludCA9IE1hdGgucm91bmQoc3RhdGljSW1hZ2VEYXRhLndpZHRoICogcmF0aW8pXG4gICAgICB9XG4gICAgfVxuICB9XG4gIHNyYyA9IHR5cGVvZiBzcmMgPT09ICdzdHJpbmcnID8gc3JjIDogc3RhdGljU3JjXG5cbiAgbGV0IGlzTGF6eSA9XG4gICAgIXByaW9yaXR5ICYmIChsb2FkaW5nID09PSAnbGF6eScgfHwgdHlwZW9mIGxvYWRpbmcgPT09ICd1bmRlZmluZWQnKVxuICBpZiAoIXNyYyB8fCBzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSB8fCBzcmMuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgIC8vIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2RvY3MvV2ViL0hUVFAvQmFzaWNzX29mX0hUVFAvRGF0YV9VUklzXG4gICAgdW5vcHRpbWl6ZWQgPSB0cnVlXG4gICAgaXNMYXp5ID0gZmFsc2VcbiAgfVxuICBpZiAoY29uZmlnLnVub3B0aW1pemVkKSB7XG4gICAgdW5vcHRpbWl6ZWQgPSB0cnVlXG4gIH1cbiAgaWYgKFxuICAgIGlzRGVmYXVsdExvYWRlciAmJlxuICAgICFjb25maWcuZGFuZ2Vyb3VzbHlBbGxvd1NWRyAmJlxuICAgIHNyYy5zcGxpdCgnPycsIDEpWzBdLmVuZHNXaXRoKCcuc3ZnJylcbiAgKSB7XG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIG1ha2Ugc3ZnIHNlcnZlIGFzLWlzIHRvIGF2b2lkIHByb3h5aW5nXG4gICAgLy8gdGhyb3VnaCB0aGUgYnVpbHQtaW4gSW1hZ2UgT3B0aW1pemF0aW9uIEFQSS5cbiAgICB1bm9wdGltaXplZCA9IHRydWVcbiAgfVxuXG4gIGNvbnN0IHF1YWxpdHlJbnQgPSBnZXRJbnQocXVhbGl0eSlcblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGlmIChjb25maWcub3V0cHV0ID09PSAnZXhwb3J0JyAmJiBpc0RlZmF1bHRMb2FkZXIgJiYgIXVub3B0aW1pemVkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbWFnZSBPcHRpbWl6YXRpb24gdXNpbmcgdGhlIGRlZmF1bHQgbG9hZGVyIGlzIG5vdCBjb21wYXRpYmxlIHdpdGggXFxgeyBvdXRwdXQ6ICdleHBvcnQnIH1cXGAuXG4gIFBvc3NpYmxlIHNvbHV0aW9uczpcbiAgICAtIFJlbW92ZSBcXGB7IG91dHB1dDogJ2V4cG9ydCcgfVxcYCBhbmQgcnVuIFwibmV4dCBzdGFydFwiIHRvIHJ1biBzZXJ2ZXIgbW9kZSBpbmNsdWRpbmcgdGhlIEltYWdlIE9wdGltaXphdGlvbiBBUEkuXG4gICAgLSBDb25maWd1cmUgXFxgeyBpbWFnZXM6IHsgdW5vcHRpbWl6ZWQ6IHRydWUgfSB9XFxgIGluIFxcYG5leHQuY29uZmlnLmpzXFxgIHRvIGRpc2FibGUgdGhlIEltYWdlIE9wdGltaXphdGlvbiBBUEkuXG4gIFJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvZXhwb3J0LWltYWdlLWFwaWBcbiAgICAgIClcbiAgICB9XG4gICAgaWYgKCFzcmMpIHtcbiAgICAgIC8vIFJlYWN0IGRvZXNuJ3Qgc2hvdyB0aGUgc3RhY2sgdHJhY2UgYW5kIHRoZXJlJ3NcbiAgICAgIC8vIG5vIGBzcmNgIHRvIGhlbHAgaWRlbnRpZnkgd2hpY2ggaW1hZ2UsIHNvIHdlXG4gICAgICAvLyBpbnN0ZWFkIGNvbnNvbGUuZXJyb3IocmVmKSBkdXJpbmcgbW91bnQuXG4gICAgICB1bm9wdGltaXplZCA9IHRydWVcbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKGZpbGwpIHtcbiAgICAgICAgaWYgKHdpZHRoKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgICAgYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGJvdGggXCJ3aWR0aFwiIGFuZCBcImZpbGxcIiBwcm9wZXJ0aWVzLiBPbmx5IG9uZSBzaG91bGQgYmUgdXNlZC5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGlmIChoZWlnaHQpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgYm90aCBcImhlaWdodFwiIGFuZCBcImZpbGxcIiBwcm9wZXJ0aWVzLiBPbmx5IG9uZSBzaG91bGQgYmUgdXNlZC5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGlmIChzdHlsZT8ucG9zaXRpb24gJiYgc3R5bGUucG9zaXRpb24gIT09ICdhYnNvbHV0ZScpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgYm90aCBcImZpbGxcIiBhbmQgXCJzdHlsZS5wb3NpdGlvblwiIHByb3BlcnRpZXMuIEltYWdlcyB3aXRoIFwiZmlsbFwiIGFsd2F5cyB1c2UgcG9zaXRpb24gYWJzb2x1dGUgLSBpdCBjYW5ub3QgYmUgbW9kaWZpZWQuYFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgICBpZiAoc3R5bGU/LndpZHRoICYmIHN0eWxlLndpZHRoICE9PSAnMTAwJScpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgYm90aCBcImZpbGxcIiBhbmQgXCJzdHlsZS53aWR0aFwiIHByb3BlcnRpZXMuIEltYWdlcyB3aXRoIFwiZmlsbFwiIGFsd2F5cyB1c2Ugd2lkdGggMTAwJSAtIGl0IGNhbm5vdCBiZSBtb2RpZmllZC5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGlmIChzdHlsZT8uaGVpZ2h0ICYmIHN0eWxlLmhlaWdodCAhPT0gJzEwMCUnKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgICAgYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGJvdGggXCJmaWxsXCIgYW5kIFwic3R5bGUuaGVpZ2h0XCIgcHJvcGVydGllcy4gSW1hZ2VzIHdpdGggXCJmaWxsXCIgYWx3YXlzIHVzZSBoZWlnaHQgMTAwJSAtIGl0IGNhbm5vdCBiZSBtb2RpZmllZC5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAodHlwZW9mIHdpZHRoSW50ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJ3aWR0aFwiIHByb3BlcnR5LmBcbiAgICAgICAgICApXG4gICAgICAgIH0gZWxzZSBpZiAoaXNOYU4od2lkdGhJbnQpKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgICAgYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGludmFsaWQgXCJ3aWR0aFwiIHByb3BlcnR5LiBFeHBlY3RlZCBhIG51bWVyaWMgdmFsdWUgaW4gcGl4ZWxzIGJ1dCByZWNlaXZlZCBcIiR7d2lkdGh9XCIuYFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodEludCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBpcyBtaXNzaW5nIHJlcXVpcmVkIFwiaGVpZ2h0XCIgcHJvcGVydHkuYFxuICAgICAgICAgIClcbiAgICAgICAgfSBlbHNlIGlmIChpc05hTihoZWlnaHRJbnQpKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgICAgYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGludmFsaWQgXCJoZWlnaHRcIiBwcm9wZXJ0eS4gRXhwZWN0ZWQgYSBudW1lcmljIHZhbHVlIGluIHBpeGVscyBidXQgcmVjZWl2ZWQgXCIke2hlaWdodH1cIi5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb250cm9sLXJlZ2V4XG4gICAgICAgIGlmICgvXltcXHgwMC1cXHgyMF0vLnRlc3Qoc3JjKSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGNhbm5vdCBzdGFydCB3aXRoIGEgc3BhY2Ugb3IgY29udHJvbCBjaGFyYWN0ZXIuIFVzZSBzcmMudHJpbVN0YXJ0KCkgdG8gcmVtb3ZlIGl0IG9yIGVuY29kZVVSSUNvbXBvbmVudChzcmMpIHRvIGtlZXAgaXQuYFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29udHJvbC1yZWdleFxuICAgICAgICBpZiAoL1tcXHgwMC1cXHgyMF0kLy50ZXN0KHNyYykpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBjYW5ub3QgZW5kIHdpdGggYSBzcGFjZSBvciBjb250cm9sIGNoYXJhY3Rlci4gVXNlIHNyYy50cmltRW5kKCkgdG8gcmVtb3ZlIGl0IG9yIGVuY29kZVVSSUNvbXBvbmVudChzcmMpIHRvIGtlZXAgaXQuYFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBpZiAoIVZBTElEX0xPQURJTkdfVkFMVUVTLmluY2x1ZGVzKGxvYWRpbmcpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBpbnZhbGlkIFwibG9hZGluZ1wiIHByb3BlcnR5LiBQcm92aWRlZCBcIiR7bG9hZGluZ31cIiBzaG91bGQgYmUgb25lIG9mICR7VkFMSURfTE9BRElOR19WQUxVRVMubWFwKFxuICAgICAgICAgIFN0cmluZ1xuICAgICAgICApLmpvaW4oJywnKX0uYFxuICAgICAgKVxuICAgIH1cbiAgICBpZiAocHJpb3JpdHkgJiYgbG9hZGluZyA9PT0gJ2xhenknKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBib3RoIFwicHJpb3JpdHlcIiBhbmQgXCJsb2FkaW5nPSdsYXp5J1wiIHByb3BlcnRpZXMuIE9ubHkgb25lIHNob3VsZCBiZSB1c2VkLmBcbiAgICAgIClcbiAgICB9XG4gICAgaWYgKFxuICAgICAgcGxhY2Vob2xkZXIgIT09ICdlbXB0eScgJiZcbiAgICAgIHBsYWNlaG9sZGVyICE9PSAnYmx1cicgJiZcbiAgICAgICFwbGFjZWhvbGRlci5zdGFydHNXaXRoKCdkYXRhOmltYWdlLycpXG4gICAgKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBpbnZhbGlkIFwicGxhY2Vob2xkZXJcIiBwcm9wZXJ0eSBcIiR7cGxhY2Vob2xkZXJ9XCIuYFxuICAgICAgKVxuICAgIH1cbiAgICBpZiAocGxhY2Vob2xkZXIgIT09ICdlbXB0eScpIHtcbiAgICAgIGlmICh3aWR0aEludCAmJiBoZWlnaHRJbnQgJiYgd2lkdGhJbnQgKiBoZWlnaHRJbnQgPCAxNjAwKSB7XG4gICAgICAgIHdhcm5PbmNlKFxuICAgICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIHNtYWxsZXIgdGhhbiA0MHg0MC4gQ29uc2lkZXIgcmVtb3ZpbmcgdGhlIFwicGxhY2Vob2xkZXJcIiBwcm9wZXJ0eSB0byBpbXByb3ZlIHBlcmZvcm1hbmNlLmBcbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cbiAgICBpZiAocGxhY2Vob2xkZXIgPT09ICdibHVyJyAmJiAhYmx1ckRhdGFVUkwpIHtcbiAgICAgIGNvbnN0IFZBTElEX0JMVVJfRVhUID0gWydqcGVnJywgJ3BuZycsICd3ZWJwJywgJ2F2aWYnXSAvLyBzaG91bGQgbWF0Y2ggbmV4dC1pbWFnZS1sb2FkZXJcblxuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgXCJwbGFjZWhvbGRlcj0nYmx1cidcIiBwcm9wZXJ0eSBidXQgaXMgbWlzc2luZyB0aGUgXCJibHVyRGF0YVVSTFwiIHByb3BlcnR5LlxuICAgICAgICBQb3NzaWJsZSBzb2x1dGlvbnM6XG4gICAgICAgICAgLSBBZGQgYSBcImJsdXJEYXRhVVJMXCIgcHJvcGVydHksIHRoZSBjb250ZW50cyBzaG91bGQgYmUgYSBzbWFsbCBEYXRhIFVSTCB0byByZXByZXNlbnQgdGhlIGltYWdlXG4gICAgICAgICAgLSBDaGFuZ2UgdGhlIFwic3JjXCIgcHJvcGVydHkgdG8gYSBzdGF0aWMgaW1wb3J0IHdpdGggb25lIG9mIHRoZSBzdXBwb3J0ZWQgZmlsZSB0eXBlczogJHtWQUxJRF9CTFVSX0VYVC5qb2luKFxuICAgICAgICAgICAgJywnXG4gICAgICAgICAgKX0gKGFuaW1hdGVkIGltYWdlcyBub3Qgc3VwcG9ydGVkKVxuICAgICAgICAgIC0gUmVtb3ZlIHRoZSBcInBsYWNlaG9sZGVyXCIgcHJvcGVydHksIGVmZmVjdGl2ZWx5IG5vIGJsdXIgZWZmZWN0XG4gICAgICAgIFJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvcGxhY2Vob2xkZXItYmx1ci1kYXRhLXVybGBcbiAgICAgIClcbiAgICB9XG4gICAgaWYgKCdyZWYnIGluIHJlc3QpIHtcbiAgICAgIHdhcm5PbmNlKFxuICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBpcyB1c2luZyB1bnN1cHBvcnRlZCBcInJlZlwiIHByb3BlcnR5LiBDb25zaWRlciB1c2luZyB0aGUgXCJvbkxvYWRcIiBwcm9wZXJ0eSBpbnN0ZWFkLmBcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAoIXVub3B0aW1pemVkICYmICFpc0RlZmF1bHRMb2FkZXIpIHtcbiAgICAgIGNvbnN0IHVybFN0ciA9IGxvYWRlcih7XG4gICAgICAgIGNvbmZpZyxcbiAgICAgICAgc3JjLFxuICAgICAgICB3aWR0aDogd2lkdGhJbnQgfHwgNDAwLFxuICAgICAgICBxdWFsaXR5OiBxdWFsaXR5SW50IHx8IDc1LFxuICAgICAgfSlcbiAgICAgIGxldCB1cmw6IFVSTCB8IHVuZGVmaW5lZFxuICAgICAgdHJ5IHtcbiAgICAgICAgdXJsID0gbmV3IFVSTCh1cmxTdHIpXG4gICAgICB9IGNhdGNoIChlcnIpIHt9XG4gICAgICBpZiAodXJsU3RyID09PSBzcmMgfHwgKHVybCAmJiB1cmwucGF0aG5hbWUgPT09IHNyYyAmJiAhdXJsLnNlYXJjaCkpIHtcbiAgICAgICAgd2Fybk9uY2UoXG4gICAgICAgICAgYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGEgXCJsb2FkZXJcIiBwcm9wZXJ0eSB0aGF0IGRvZXMgbm90IGltcGxlbWVudCB3aWR0aC4gUGxlYXNlIGltcGxlbWVudCBpdCBvciB1c2UgdGhlIFwidW5vcHRpbWl6ZWRcIiBwcm9wZXJ0eSBpbnN0ZWFkLmAgK1xuICAgICAgICAgICAgYFxcblJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbmV4dC1pbWFnZS1taXNzaW5nLWxvYWRlci13aWR0aGBcbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChvbkxvYWRpbmdDb21wbGV0ZSkge1xuICAgICAgd2Fybk9uY2UoXG4gICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIHVzaW5nIGRlcHJlY2F0ZWQgXCJvbkxvYWRpbmdDb21wbGV0ZVwiIHByb3BlcnR5LiBQbGVhc2UgdXNlIHRoZSBcIm9uTG9hZFwiIHByb3BlcnR5IGluc3RlYWQuYFxuICAgICAgKVxuICAgIH1cblxuICAgIGZvciAoY29uc3QgW2xlZ2FjeUtleSwgbGVnYWN5VmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHtcbiAgICAgIGxheW91dCxcbiAgICAgIG9iamVjdEZpdCxcbiAgICAgIG9iamVjdFBvc2l0aW9uLFxuICAgICAgbGF6eUJvdW5kYXJ5LFxuICAgICAgbGF6eVJvb3QsXG4gICAgfSkpIHtcbiAgICAgIGlmIChsZWdhY3lWYWx1ZSkge1xuICAgICAgICB3YXJuT25jZShcbiAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgbGVnYWN5IHByb3AgXCIke2xlZ2FjeUtleX1cIi4gRGlkIHlvdSBmb3JnZXQgdG8gcnVuIHRoZSBjb2RlbW9kP2AgK1xuICAgICAgICAgICAgYFxcblJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbmV4dC1pbWFnZS11cGdyYWRlLXRvLTEzYFxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKFxuICAgICAgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICAgICFwZXJmT2JzZXJ2ZXIgJiZcbiAgICAgIHdpbmRvdy5QZXJmb3JtYW5jZU9ic2VydmVyXG4gICAgKSB7XG4gICAgICBwZXJmT2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigoZW50cnlMaXN0KSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgZW50cnkgb2YgZW50cnlMaXN0LmdldEVudHJpZXMoKSkge1xuICAgICAgICAgIC8vIEB0cy1pZ25vcmUgLSBtaXNzaW5nIFwiTGFyZ2VzdENvbnRlbnRmdWxQYWludFwiIGNsYXNzIHdpdGggXCJlbGVtZW50XCIgcHJvcFxuICAgICAgICAgIGNvbnN0IGltZ1NyYyA9IGVudHJ5Py5lbGVtZW50Py5zcmMgfHwgJydcbiAgICAgICAgICBjb25zdCBsY3BJbWFnZSA9IGFsbEltZ3MuZ2V0KGltZ1NyYylcbiAgICAgICAgICBpZiAoXG4gICAgICAgICAgICBsY3BJbWFnZSAmJlxuICAgICAgICAgICAgIWxjcEltYWdlLnByaW9yaXR5ICYmXG4gICAgICAgICAgICBsY3BJbWFnZS5wbGFjZWhvbGRlciA9PT0gJ2VtcHR5JyAmJlxuICAgICAgICAgICAgIWxjcEltYWdlLnNyYy5zdGFydHNXaXRoKCdkYXRhOicpICYmXG4gICAgICAgICAgICAhbGNwSW1hZ2Uuc3JjLnN0YXJ0c1dpdGgoJ2Jsb2I6JylcbiAgICAgICAgICApIHtcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vd2ViLmRldi9sY3AvI21lYXN1cmUtbGNwLWluLWphdmFzY3JpcHRcbiAgICAgICAgICAgIHdhcm5PbmNlKFxuICAgICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke2xjcEltYWdlLnNyY31cIiB3YXMgZGV0ZWN0ZWQgYXMgdGhlIExhcmdlc3QgQ29udGVudGZ1bCBQYWludCAoTENQKS4gUGxlYXNlIGFkZCB0aGUgXCJwcmlvcml0eVwiIHByb3BlcnR5IGlmIHRoaXMgaW1hZ2UgaXMgYWJvdmUgdGhlIGZvbGQuYCArXG4gICAgICAgICAgICAgICAgYFxcblJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2ltYWdlI3ByaW9yaXR5YFxuICAgICAgICAgICAgKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSlcbiAgICAgIHRyeSB7XG4gICAgICAgIHBlcmZPYnNlcnZlci5vYnNlcnZlKHtcbiAgICAgICAgICB0eXBlOiAnbGFyZ2VzdC1jb250ZW50ZnVsLXBhaW50JyxcbiAgICAgICAgICBidWZmZXJlZDogdHJ1ZSxcbiAgICAgICAgfSlcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAvLyBMb2cgZXJyb3IgYnV0IGRvbid0IGNyYXNoIHRoZSBhcHBcbiAgICAgICAgY29uc29sZS5lcnJvcihlcnIpXG4gICAgICB9XG4gICAgfVxuICB9XG4gIGNvbnN0IGltZ1N0eWxlID0gT2JqZWN0LmFzc2lnbihcbiAgICBmaWxsXG4gICAgICA/IHtcbiAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgIHJpZ2h0OiAwLFxuICAgICAgICAgIGJvdHRvbTogMCxcbiAgICAgICAgICBvYmplY3RGaXQsXG4gICAgICAgICAgb2JqZWN0UG9zaXRpb24sXG4gICAgICAgIH1cbiAgICAgIDoge30sXG4gICAgc2hvd0FsdFRleHQgPyB7fSA6IHsgY29sb3I6ICd0cmFuc3BhcmVudCcgfSxcbiAgICBzdHlsZVxuICApXG5cbiAgY29uc3QgYmFja2dyb3VuZEltYWdlID1cbiAgICAhYmx1ckNvbXBsZXRlICYmIHBsYWNlaG9sZGVyICE9PSAnZW1wdHknXG4gICAgICA/IHBsYWNlaG9sZGVyID09PSAnYmx1cidcbiAgICAgICAgPyBgdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sO2NoYXJzZXQ9dXRmLTgsJHtnZXRJbWFnZUJsdXJTdmcoe1xuICAgICAgICAgICAgd2lkdGhJbnQsXG4gICAgICAgICAgICBoZWlnaHRJbnQsXG4gICAgICAgICAgICBibHVyV2lkdGgsXG4gICAgICAgICAgICBibHVySGVpZ2h0LFxuICAgICAgICAgICAgYmx1ckRhdGFVUkw6IGJsdXJEYXRhVVJMIHx8ICcnLCAvLyBhc3N1bWUgbm90IHVuZGVmaW5lZFxuICAgICAgICAgICAgb2JqZWN0Rml0OiBpbWdTdHlsZS5vYmplY3RGaXQsXG4gICAgICAgICAgfSl9XCIpYFxuICAgICAgICA6IGB1cmwoXCIke3BsYWNlaG9sZGVyfVwiKWAgLy8gYXNzdW1lIGBkYXRhOmltYWdlL2BcbiAgICAgIDogbnVsbFxuXG4gIGxldCBwbGFjZWhvbGRlclN0eWxlID0gYmFja2dyb3VuZEltYWdlXG4gICAgPyB7XG4gICAgICAgIGJhY2tncm91bmRTaXplOiBpbWdTdHlsZS5vYmplY3RGaXQgfHwgJ2NvdmVyJyxcbiAgICAgICAgYmFja2dyb3VuZFBvc2l0aW9uOiBpbWdTdHlsZS5vYmplY3RQb3NpdGlvbiB8fCAnNTAlIDUwJScsXG4gICAgICAgIGJhY2tncm91bmRSZXBlYXQ6ICduby1yZXBlYXQnLFxuICAgICAgICBiYWNrZ3JvdW5kSW1hZ2UsXG4gICAgICB9XG4gICAgOiB7fVxuXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgIGlmIChcbiAgICAgIHBsYWNlaG9sZGVyU3R5bGUuYmFja2dyb3VuZEltYWdlICYmXG4gICAgICBwbGFjZWhvbGRlciA9PT0gJ2JsdXInICYmXG4gICAgICBibHVyRGF0YVVSTD8uc3RhcnRzV2l0aCgnLycpXG4gICAgKSB7XG4gICAgICAvLyBEdXJpbmcgYG5leHQgZGV2YCwgd2UgZG9uJ3Qgd2FudCB0byBnZW5lcmF0ZSBibHVyIHBsYWNlaG9sZGVycyB3aXRoIHdlYnBhY2tcbiAgICAgIC8vIGJlY2F1c2UgaXQgY2FuIGRlbGF5IHN0YXJ0aW5nIHRoZSBkZXYgc2VydmVyLiBJbnN0ZWFkLCBgbmV4dC1pbWFnZS1sb2FkZXIuanNgXG4gICAgICAvLyB3aWxsIGlubGluZSBhIHNwZWNpYWwgdXJsIHRvIGxhemlseSBnZW5lcmF0ZSB0aGUgYmx1ciBwbGFjZWhvbGRlciBhdCByZXF1ZXN0IHRpbWUuXG4gICAgICBwbGFjZWhvbGRlclN0eWxlLmJhY2tncm91bmRJbWFnZSA9IGB1cmwoXCIke2JsdXJEYXRhVVJMfVwiKWBcbiAgICB9XG4gIH1cblxuICBjb25zdCBpbWdBdHRyaWJ1dGVzID0gZ2VuZXJhdGVJbWdBdHRycyh7XG4gICAgY29uZmlnLFxuICAgIHNyYyxcbiAgICB1bm9wdGltaXplZCxcbiAgICB3aWR0aDogd2lkdGhJbnQsXG4gICAgcXVhbGl0eTogcXVhbGl0eUludCxcbiAgICBzaXplcyxcbiAgICBsb2FkZXIsXG4gIH0pXG5cbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxldCBmdWxsVXJsOiBVUkxcbiAgICAgIHRyeSB7XG4gICAgICAgIGZ1bGxVcmwgPSBuZXcgVVJMKGltZ0F0dHJpYnV0ZXMuc3JjKVxuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBmdWxsVXJsID0gbmV3IFVSTChpbWdBdHRyaWJ1dGVzLnNyYywgd2luZG93LmxvY2F0aW9uLmhyZWYpXG4gICAgICB9XG4gICAgICBhbGxJbWdzLnNldChmdWxsVXJsLmhyZWYsIHsgc3JjLCBwcmlvcml0eSwgcGxhY2Vob2xkZXIgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBwcm9wczogSW1nUHJvcHMgPSB7XG4gICAgLi4ucmVzdCxcbiAgICBsb2FkaW5nOiBpc0xhenkgPyAnbGF6eScgOiBsb2FkaW5nLFxuICAgIGZldGNoUHJpb3JpdHksXG4gICAgd2lkdGg6IHdpZHRoSW50LFxuICAgIGhlaWdodDogaGVpZ2h0SW50LFxuICAgIGRlY29kaW5nLFxuICAgIGNsYXNzTmFtZSxcbiAgICBzdHlsZTogeyAuLi5pbWdTdHlsZSwgLi4ucGxhY2Vob2xkZXJTdHlsZSB9LFxuICAgIHNpemVzOiBpbWdBdHRyaWJ1dGVzLnNpemVzLFxuICAgIHNyY1NldDogaW1nQXR0cmlidXRlcy5zcmNTZXQsXG4gICAgc3JjOiBvdmVycmlkZVNyYyB8fCBpbWdBdHRyaWJ1dGVzLnNyYyxcbiAgfVxuICBjb25zdCBtZXRhID0geyB1bm9wdGltaXplZCwgcHJpb3JpdHksIHBsYWNlaG9sZGVyLCBmaWxsIH1cbiAgcmV0dXJuIHsgcHJvcHMsIG1ldGEgfVxufVxuIl0sIm5hbWVzIjpbImdldEltZ1Byb3BzIiwiVkFMSURfTE9BRElOR19WQUxVRVMiLCJ1bmRlZmluZWQiLCJpc1N0YXRpY1JlcXVpcmUiLCJzcmMiLCJkZWZhdWx0IiwiaXNTdGF0aWNJbWFnZURhdGEiLCJpc1N0YXRpY0ltcG9ydCIsImFsbEltZ3MiLCJNYXAiLCJwZXJmT2JzZXJ2ZXIiLCJnZXRJbnQiLCJ4IiwiTnVtYmVyIiwiaXNGaW5pdGUiLCJOYU4iLCJ0ZXN0IiwicGFyc2VJbnQiLCJnZXRXaWR0aHMiLCJ3aWR0aCIsInNpemVzIiwiZGV2aWNlU2l6ZXMiLCJhbGxTaXplcyIsInZpZXdwb3J0V2lkdGhSZSIsInBlcmNlbnRTaXplcyIsIm1hdGNoIiwiZXhlYyIsInB1c2giLCJsZW5ndGgiLCJzbWFsbGVzdFJhdGlvIiwiTWF0aCIsIm1pbiIsIndpZHRocyIsImZpbHRlciIsInMiLCJraW5kIiwiU2V0IiwibWFwIiwidyIsImZpbmQiLCJwIiwiZ2VuZXJhdGVJbWdBdHRycyIsImNvbmZpZyIsInVub3B0aW1pemVkIiwicXVhbGl0eSIsImxvYWRlciIsInNyY1NldCIsImxhc3QiLCJpIiwiam9pbiIsIl9zdGF0ZSIsInByaW9yaXR5IiwibG9hZGluZyIsImNsYXNzTmFtZSIsImhlaWdodCIsImZpbGwiLCJzdHlsZSIsIm92ZXJyaWRlU3JjIiwib25Mb2FkIiwib25Mb2FkaW5nQ29tcGxldGUiLCJwbGFjZWhvbGRlciIsImJsdXJEYXRhVVJMIiwiZmV0Y2hQcmlvcml0eSIsImRlY29kaW5nIiwibGF5b3V0Iiwib2JqZWN0Rml0Iiwib2JqZWN0UG9zaXRpb24iLCJsYXp5Qm91bmRhcnkiLCJsYXp5Um9vdCIsInJlc3QiLCJpbWdDb25mIiwic2hvd0FsdFRleHQiLCJibHVyQ29tcGxldGUiLCJkZWZhdWx0TG9hZGVyIiwiYyIsImltYWdlQ29uZmlnRGVmYXVsdCIsImltYWdlU2l6ZXMiLCJzb3J0IiwiYSIsImIiLCJxdWFsaXRpZXMiLCJFcnJvciIsImlzRGVmYXVsdExvYWRlciIsImN1c3RvbUltYWdlTG9hZGVyIiwib2JqIiwiXyIsIm9wdHMiLCJsYXlvdXRUb1N0eWxlIiwiaW50cmluc2ljIiwibWF4V2lkdGgiLCJyZXNwb25zaXZlIiwibGF5b3V0VG9TaXplcyIsImxheW91dFN0eWxlIiwibGF5b3V0U2l6ZXMiLCJzdGF0aWNTcmMiLCJ3aWR0aEludCIsImhlaWdodEludCIsImJsdXJXaWR0aCIsImJsdXJIZWlnaHQiLCJzdGF0aWNJbWFnZURhdGEiLCJKU09OIiwic3RyaW5naWZ5IiwicmF0aW8iLCJyb3VuZCIsImlzTGF6eSIsInN0YXJ0c1dpdGgiLCJkYW5nZXJvdXNseUFsbG93U1ZHIiwic3BsaXQiLCJlbmRzV2l0aCIsInF1YWxpdHlJbnQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJvdXRwdXQiLCJwb3NpdGlvbiIsImlzTmFOIiwiaW5jbHVkZXMiLCJTdHJpbmciLCJ3YXJuT25jZSIsIlZBTElEX0JMVVJfRVhUIiwidXJsU3RyIiwidXJsIiwiVVJMIiwiZXJyIiwicGF0aG5hbWUiLCJzZWFyY2giLCJsZWdhY3lLZXkiLCJsZWdhY3lWYWx1ZSIsIk9iamVjdCIsImVudHJpZXMiLCJ3aW5kb3ciLCJQZXJmb3JtYW5jZU9ic2VydmVyIiwiZW50cnlMaXN0IiwiZW50cnkiLCJnZXRFbnRyaWVzIiwiaW1nU3JjIiwiZWxlbWVudCIsImxjcEltYWdlIiwiZ2V0Iiwib2JzZXJ2ZSIsInR5cGUiLCJidWZmZXJlZCIsImNvbnNvbGUiLCJlcnJvciIsImltZ1N0eWxlIiwiYXNzaWduIiwibGVmdCIsInRvcCIsInJpZ2h0IiwiYm90dG9tIiwiY29sb3IiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJnZXRJbWFnZUJsdXJTdmciLCJwbGFjZWhvbGRlclN0eWxlIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUG9zaXRpb24iLCJiYWNrZ3JvdW5kUmVwZWF0IiwiaW1nQXR0cmlidXRlcyIsImZ1bGxVcmwiLCJlIiwibG9jYXRpb24iLCJocmVmIiwic2V0IiwicHJvcHMiLCJtZXRhIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        }, \"charset\")\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }, \"viewport\"));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === 'string' || typeof child === 'number') {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    'name',\n    'httpEquiv',\n    'charSet',\n    'itemProp'\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf('$') + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case 'title':\n            case 'base':\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case 'meta':\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === 'charSet') {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n                const srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-blur-svg.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getImageBlurSvg\", ({\n    enumerable: true,\n    get: function() {\n        return getImageBlurSvg;\n    }\n}));\nfunction getImageBlurSvg(param) {\n    let { widthInt, heightInt, blurWidth, blurHeight, blurDataURL, objectFit } = param;\n    const std = 20;\n    const svgWidth = blurWidth ? blurWidth * 40 : widthInt;\n    const svgHeight = blurHeight ? blurHeight * 40 : heightInt;\n    const viewBox = svgWidth && svgHeight ? \"viewBox='0 0 \" + svgWidth + \" \" + svgHeight + \"'\" : '';\n    const preserveAspectRatio = viewBox ? 'none' : objectFit === 'contain' ? 'xMidYMid' : objectFit === 'cover' ? 'xMidYMid slice' : 'none';\n    return \"%3Csvg xmlns='http://www.w3.org/2000/svg' \" + viewBox + \"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='\" + std + \"'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='\" + std + \"'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='\" + preserveAspectRatio + \"' style='filter: url(%23b);' href='\" + blurDataURL + \"'/%3E%3C/svg%3E\";\n} //# sourceMappingURL=image-blur-svg.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ImageConfigContext\", ({\n    enumerable: true,\n    get: function() {\n        return ImageConfigContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _imageconfig = __webpack_require__(/*! ./image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst ImageConfigContext = _react.default.createContext(_imageconfig.imageConfigDefault);\nif (true) {\n    ImageConfigContext.displayName = 'ImageConfigContext';\n} //# sourceMappingURL=image-config-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbWFnZS1jb25maWctY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUlhQTs7O2VBQUFBOzs7OzRFQUpLO3lDQUVpQjtBQUU1QixNQUFNQSxxQkFDWEMsT0FBQUEsT0FBSyxDQUFDQyxhQUFhLENBQXNCQyxhQUFBQSxrQkFBa0I7QUFFN0QsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekNKLG1CQUFtQk8sV0FBVyxHQUFHO0FBQ25DIiwic291cmNlcyI6WyJEOlxcc3JjXFxzaGFyZWRcXGxpYlxcaW1hZ2UtY29uZmlnLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHR5cGUgeyBJbWFnZUNvbmZpZ0NvbXBsZXRlIH0gZnJvbSAnLi9pbWFnZS1jb25maWcnXG5pbXBvcnQgeyBpbWFnZUNvbmZpZ0RlZmF1bHQgfSBmcm9tICcuL2ltYWdlLWNvbmZpZydcblxuZXhwb3J0IGNvbnN0IEltYWdlQ29uZmlnQ29udGV4dCA9XG4gIFJlYWN0LmNyZWF0ZUNvbnRleHQ8SW1hZ2VDb25maWdDb21wbGV0ZT4oaW1hZ2VDb25maWdEZWZhdWx0KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBJbWFnZUNvbmZpZ0NvbnRleHQuZGlzcGxheU5hbWUgPSAnSW1hZ2VDb25maWdDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIkltYWdlQ29uZmlnQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsImltYWdlQ29uZmlnRGVmYXVsdCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-config.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    VALID_LOADERS: function() {\n        return VALID_LOADERS;\n    },\n    imageConfigDefault: function() {\n        return imageConfigDefault;\n    }\n});\nconst VALID_LOADERS = [\n    'default',\n    'imgix',\n    'cloudinary',\n    'akamai',\n    'custom'\n];\nconst imageConfigDefault = {\n    deviceSizes: [\n        640,\n        750,\n        828,\n        1080,\n        1200,\n        1920,\n        2048,\n        3840\n    ],\n    imageSizes: [\n        16,\n        32,\n        48,\n        64,\n        96,\n        128,\n        256,\n        384\n    ],\n    path: '/_next/image',\n    loader: 'default',\n    loaderFile: '',\n    domains: [],\n    disableStaticImages: false,\n    minimumCacheTTL: 60,\n    formats: [\n        'image/webp'\n    ],\n    dangerouslyAllowSVG: false,\n    contentSecurityPolicy: \"script-src 'none'; frame-src 'none'; sandbox;\",\n    contentDispositionType: 'attachment',\n    localPatterns: undefined,\n    remotePatterns: [],\n    qualities: undefined,\n    unoptimized: false\n}; //# sourceMappingURL=image-config.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-external.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    getImageProps: function() {\n        return getImageProps;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _getimgprops = __webpack_require__(/*! ./get-img-props */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\");\nconst _imagecomponent = __webpack_require__(/*! ../../client/image-component */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\");\nconst _imageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/shared/lib/image-loader */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\"));\nfunction getImageProps(imgProps) {\n    const { props } = (0, _getimgprops.getImgProps)(imgProps, {\n        defaultLoader: _imageloader.default,\n        // This is replaced by webpack define plugin\n        imgConf: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[]}\n    });\n    // Normally we don't care about undefined props because we pass to JSX,\n    // but this exported function could be used by the end user for anything\n    // so we delete undefined props to clean it up a little.\n    for (const [key, value] of Object.entries(props)){\n        if (value === undefined) {\n            delete props[key];\n        }\n    }\n    return {\n        props\n    };\n}\nconst _default = _imagecomponent.Image; //# sourceMappingURL=image-external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-loader.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst DEFAULT_Q = 75;\nfunction defaultLoader(param) {\n    let { config, src, width, quality } = param;\n    var _config_qualities;\n    if (true) {\n        const missingValues = [];\n        // these should always be provided but make sure they are\n        if (!src) missingValues.push('src');\n        if (!width) missingValues.push('width');\n        if (missingValues.length > 0) {\n            throw new Error(\"Next Image Optimization requires \" + missingValues.join(', ') + \" to be provided. Make sure you pass them as props to the `next/image` component. Received: \" + JSON.stringify({\n                src,\n                width,\n                quality\n            }));\n        }\n        if (src.startsWith('//')) {\n            throw new Error('Failed to parse src \"' + src + '\" on `next/image`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)');\n        }\n        if (src.startsWith('/') && config.localPatterns) {\n            if (true) {\n                // We use dynamic require because this should only error in development\n                const { hasLocalMatch } = __webpack_require__(/*! ./match-local-pattern */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js\");\n                if (!hasLocalMatch(config.localPatterns, src)) {\n                    throw new Error(\"Invalid src prop (\" + src + \") on `next/image` does not match `images.localPatterns` configured in your `next.config.js`\\n\" + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns\");\n                }\n            }\n        }\n        if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n            let parsedSrc;\n            try {\n                parsedSrc = new URL(src);\n            } catch (err) {\n                console.error(err);\n                throw new Error('Failed to parse src \"' + src + '\" on `next/image`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)');\n            }\n            if (true) {\n                // We use dynamic require because this should only error in development\n                const { hasRemoteMatch } = __webpack_require__(/*! ./match-remote-pattern */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js\");\n                if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n                    throw new Error(\"Invalid src prop (\" + src + ') on `next/image`, hostname \"' + parsedSrc.hostname + '\" is not configured under images in your `next.config.js`\\n' + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host\");\n                }\n            }\n        }\n        if (quality && config.qualities && !config.qualities.includes(quality)) {\n            throw new Error(\"Invalid quality prop (\" + quality + \") on `next/image` does not match `images.qualities` configured in your `next.config.js`\\n\" + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities\");\n        }\n    }\n    const q = quality || ((_config_qualities = config.qualities) == null ? void 0 : _config_qualities.reduce((prev, cur)=>Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev)) || DEFAULT_Q;\n    return config.path + \"?url=\" + encodeURIComponent(src) + \"&w=\" + width + \"&q=\" + q + (src.startsWith('/_next/static/media/') && false ? 0 : '');\n}\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true;\nconst _default = defaultLoader; //# sourceMappingURL=image-loader.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/match-local-pattern.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    hasLocalMatch: function() {\n        return hasLocalMatch;\n    },\n    matchLocalPattern: function() {\n        return matchLocalPattern;\n    }\n});\nconst _picomatch = __webpack_require__(/*! next/dist/compiled/picomatch */ \"(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js\");\nfunction matchLocalPattern(pattern, url) {\n    if (pattern.search !== undefined) {\n        if (pattern.search !== url.search) {\n            return false;\n        }\n    }\n    var _pattern_pathname;\n    if (!(0, _picomatch.makeRe)((_pattern_pathname = pattern.pathname) != null ? _pattern_pathname : '**', {\n        dot: true\n    }).test(url.pathname)) {\n        return false;\n    }\n    return true;\n}\nfunction hasLocalMatch(localPatterns, urlPathAndQuery) {\n    if (!localPatterns) {\n        // if the user didn't define \"localPatterns\", we allow all local images\n        return true;\n    }\n    const url = new URL(urlPathAndQuery, 'http://n');\n    return localPatterns.some((p)=>matchLocalPattern(p, url));\n} //# sourceMappingURL=match-local-pattern.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/match-remote-pattern.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    hasRemoteMatch: function() {\n        return hasRemoteMatch;\n    },\n    matchRemotePattern: function() {\n        return matchRemotePattern;\n    }\n});\nconst _picomatch = __webpack_require__(/*! next/dist/compiled/picomatch */ \"(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js\");\nfunction matchRemotePattern(pattern, url) {\n    if (pattern.protocol !== undefined) {\n        const actualProto = url.protocol.slice(0, -1);\n        if (pattern.protocol !== actualProto) {\n            return false;\n        }\n    }\n    if (pattern.port !== undefined) {\n        if (pattern.port !== url.port) {\n            return false;\n        }\n    }\n    if (pattern.hostname === undefined) {\n        throw new Error(\"Pattern should define hostname but found\\n\" + JSON.stringify(pattern));\n    } else {\n        if (!(0, _picomatch.makeRe)(pattern.hostname).test(url.hostname)) {\n            return false;\n        }\n    }\n    if (pattern.search !== undefined) {\n        if (pattern.search !== url.search) {\n            return false;\n        }\n    }\n    var _pattern_pathname;\n    // Should be the same as writeImagesManifest()\n    if (!(0, _picomatch.makeRe)((_pattern_pathname = pattern.pathname) != null ? _pattern_pathname : '**', {\n        dot: true\n    }).test(url.pathname)) {\n        return false;\n    }\n    return true;\n}\nfunction hasRemoteMatch(domains, remotePatterns, url) {\n    return domains.some((domain)=>url.hostname === domain) || remotePatterns.some((p)=>matchRemotePattern(p, url));\n} //# sourceMappingURL=match-remote-pattern.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9tYXRjaC1yZW1vdGUtcGF0dGVybi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF5Q2dCQSxjQUFjO2VBQWRBOztJQXJDQUMsa0JBQWtCO2VBQWxCQTs7O3VDQUhPO0FBR2hCLFNBQVNBLG1CQUFtQkMsT0FBc0IsRUFBRUMsR0FBUTtJQUNqRSxJQUFJRCxRQUFRRSxRQUFRLEtBQUtDLFdBQVc7UUFDbEMsTUFBTUMsY0FBY0gsSUFBSUMsUUFBUSxDQUFDRyxLQUFLLENBQUMsR0FBRyxDQUFDO1FBQzNDLElBQUlMLFFBQVFFLFFBQVEsS0FBS0UsYUFBYTtZQUNwQyxPQUFPO1FBQ1Q7SUFDRjtJQUNBLElBQUlKLFFBQVFNLElBQUksS0FBS0gsV0FBVztRQUM5QixJQUFJSCxRQUFRTSxJQUFJLEtBQUtMLElBQUlLLElBQUksRUFBRTtZQUM3QixPQUFPO1FBQ1Q7SUFDRjtJQUVBLElBQUlOLFFBQVFPLFFBQVEsS0FBS0osV0FBVztRQUNsQyxNQUFNLElBQUlLLE1BQ1AsK0NBQTRDQyxLQUFLQyxTQUFTLENBQUNWO0lBRWhFLE9BQU87UUFDTCxJQUFJLENBQUNXLENBQUFBLEdBQUFBLFdBQUFBLE1BQUFBLEVBQU9YLFFBQVFPLFFBQVEsRUFBRUssSUFBSSxDQUFDWCxJQUFJTSxRQUFRLEdBQUc7WUFDaEQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJUCxRQUFRYSxNQUFNLEtBQUtWLFdBQVc7UUFDaEMsSUFBSUgsUUFBUWEsTUFBTSxLQUFLWixJQUFJWSxNQUFNLEVBQUU7WUFDakMsT0FBTztRQUNUO0lBQ0Y7UUFHWWI7SUFEWiw4Q0FBOEM7SUFDOUMsSUFBSSxDQUFDVyxDQUFBQSxHQUFBQSxXQUFBQSxNQUFBQSxFQUFPWCxDQUFBQSxvQkFBQUEsUUFBUWMsUUFBQUEsS0FBUSxPQUFoQmQsb0JBQW9CLE1BQU07UUFBRWUsS0FBSztJQUFLLEdBQUdILElBQUksQ0FBQ1gsSUFBSWEsUUFBUSxHQUFHO1FBQ3ZFLE9BQU87SUFDVDtJQUVBLE9BQU87QUFDVDtBQUVPLFNBQVNoQixlQUNka0IsT0FBaUIsRUFDakJDLGNBQStCLEVBQy9CaEIsR0FBUTtJQUVSLE9BQ0VlLFFBQVFFLElBQUksQ0FBQyxDQUFDQyxTQUFXbEIsSUFBSU0sUUFBUSxLQUFLWSxXQUMxQ0YsZUFBZUMsSUFBSSxDQUFDLENBQUNFLElBQU1yQixtQkFBbUJxQixHQUFHbkI7QUFFckQiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxtYXRjaC1yZW1vdGUtcGF0dGVybi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IFJlbW90ZVBhdHRlcm4gfSBmcm9tICcuL2ltYWdlLWNvbmZpZydcbmltcG9ydCB7IG1ha2VSZSB9IGZyb20gJ25leHQvZGlzdC9jb21waWxlZC9waWNvbWF0Y2gnXG5cbi8vIE1vZGlmeWluZyB0aGlzIGZ1bmN0aW9uIHNob3VsZCBhbHNvIG1vZGlmeSB3cml0ZUltYWdlc01hbmlmZXN0KClcbmV4cG9ydCBmdW5jdGlvbiBtYXRjaFJlbW90ZVBhdHRlcm4ocGF0dGVybjogUmVtb3RlUGF0dGVybiwgdXJsOiBVUkwpOiBib29sZWFuIHtcbiAgaWYgKHBhdHRlcm4ucHJvdG9jb2wgIT09IHVuZGVmaW5lZCkge1xuICAgIGNvbnN0IGFjdHVhbFByb3RvID0gdXJsLnByb3RvY29sLnNsaWNlKDAsIC0xKVxuICAgIGlmIChwYXR0ZXJuLnByb3RvY29sICE9PSBhY3R1YWxQcm90bykge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG4gIGlmIChwYXR0ZXJuLnBvcnQgIT09IHVuZGVmaW5lZCkge1xuICAgIGlmIChwYXR0ZXJuLnBvcnQgIT09IHVybC5wb3J0KSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICBpZiAocGF0dGVybi5ob3N0bmFtZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgYFBhdHRlcm4gc2hvdWxkIGRlZmluZSBob3N0bmFtZSBidXQgZm91bmRcXG4ke0pTT04uc3RyaW5naWZ5KHBhdHRlcm4pfWBcbiAgICApXG4gIH0gZWxzZSB7XG4gICAgaWYgKCFtYWtlUmUocGF0dGVybi5ob3N0bmFtZSkudGVzdCh1cmwuaG9zdG5hbWUpKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICBpZiAocGF0dGVybi5zZWFyY2ggIT09IHVuZGVmaW5lZCkge1xuICAgIGlmIChwYXR0ZXJuLnNlYXJjaCAhPT0gdXJsLnNlYXJjaCkge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG5cbiAgLy8gU2hvdWxkIGJlIHRoZSBzYW1lIGFzIHdyaXRlSW1hZ2VzTWFuaWZlc3QoKVxuICBpZiAoIW1ha2VSZShwYXR0ZXJuLnBhdGhuYW1lID8/ICcqKicsIHsgZG90OiB0cnVlIH0pLnRlc3QodXJsLnBhdGhuYW1lKSkge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgcmV0dXJuIHRydWVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGhhc1JlbW90ZU1hdGNoKFxuICBkb21haW5zOiBzdHJpbmdbXSxcbiAgcmVtb3RlUGF0dGVybnM6IFJlbW90ZVBhdHRlcm5bXSxcbiAgdXJsOiBVUkxcbik6IGJvb2xlYW4ge1xuICByZXR1cm4gKFxuICAgIGRvbWFpbnMuc29tZSgoZG9tYWluKSA9PiB1cmwuaG9zdG5hbWUgPT09IGRvbWFpbikgfHxcbiAgICByZW1vdGVQYXR0ZXJucy5zb21lKChwKSA9PiBtYXRjaFJlbW90ZVBhdHRlcm4ocCwgdXJsKSlcbiAgKVxufVxuIl0sIm5hbWVzIjpbImhhc1JlbW90ZU1hdGNoIiwibWF0Y2hSZW1vdGVQYXR0ZXJuIiwicGF0dGVybiIsInVybCIsInByb3RvY29sIiwidW5kZWZpbmVkIiwiYWN0dWFsUHJvdG8iLCJzbGljZSIsInBvcnQiLCJob3N0bmFtZSIsIkVycm9yIiwiSlNPTiIsInN0cmluZ2lmeSIsIm1ha2VSZSIsInRlc3QiLCJzZWFyY2giLCJwYXRobmFtZSIsImRvdCIsImRvbWFpbnMiLCJyZW1vdGVQYXR0ZXJucyIsInNvbWUiLCJkb21haW4iLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router-context.shared-runtime.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouterContext\", ({\n    enumerable: true,\n    get: function() {\n        return RouterContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst RouterContext = _react.default.createContext(null);\nif (true) {\n    RouterContext.displayName = 'RouterContext';\n} //# sourceMappingURL=router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O2lEQUdhQTs7O2VBQUFBOzs7OzRFQUhLO0FBR1gsTUFBTUEsZ0JBQWdCQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBb0I7QUFFcEUsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekNILGNBQWNNLFdBQVcsR0FBRztBQUM5QiIsInNvdXJjZXMiOlsiRDpcXHNyY1xcc2hhcmVkXFxsaWJcXHJvdXRlci1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgTmV4dFJvdXRlciB9IGZyb20gJy4vcm91dGVyL3JvdXRlcidcblxuZXhwb3J0IGNvbnN0IFJvdXRlckNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0PE5leHRSb3V0ZXIgfCBudWxsPihudWxsKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBSb3V0ZXJDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1JvdXRlckNvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiUm91dGVyQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst isServer = typeof window === 'undefined';\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    var _headManager_mountedInstances;\n                    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    if (headManager) {\n                        headManager._pendingUpdate = emitChange;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    useClientOnlyEffect({\n        \"SideEffect.useClientOnlyEffect\": ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n            return ({\n                \"SideEffect.useClientOnlyEffect\": ()=>{\n                    if (headManager && headManager._pendingUpdate) {\n                        headManager._pendingUpdate();\n                        headManager._pendingUpdate = null;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyEffect\"]);\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_main_TopBanner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/main/TopBanner */ \"(app-pages-browser)/./src/components/main/TopBanner.tsx\");\n/* harmony import */ var _components_main_MainFeature__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/main/MainFeature */ \"(app-pages-browser)/./src/components/main/MainFeature.tsx\");\n/* harmony import */ var _components_main_LatestNewsPosts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/main/LatestNewsPosts */ \"(app-pages-browser)/./src/components/main/LatestNewsPosts.tsx\");\n/* harmony import */ var _components_main_PromoInvestasi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/main/PromoInvestasi */ \"(app-pages-browser)/./src/components/main/PromoInvestasi.tsx\");\n/* harmony import */ var _components_main_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/main/Sidebar */ \"(app-pages-browser)/./src/components/main/Sidebar.tsx\");\n// src/app/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Home = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 text-gray-800 shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_main_TopBanner__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_main_MainFeature__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_main_LatestNewsPosts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_main_PromoInvestasi__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_main_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Home;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/main/LatestNewsPosts.tsx":
/*!*************************************************!*\
  !*** ./src/components/main/LatestNewsPosts.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// src/components/LatestNewsPosts.tsx\n\n\nconst LatestNewsPosts = ()=>{\n    const newsItems = [\n        {\n            id: 1,\n            title: \"2024 Investasi di Pekanbaru Serap Belasan Ribu Tenaga Kerja\",\n            image: \"/berita/mainfeat.jpg\",\n            date: \"20 Mar 2025\"\n        },\n        {\n            id: 2,\n            title: \"Kepala DPMPTSP Pekanbaru Ikuti Apel Gabungan dan Pengarahan PJ Walikota\",\n            image: \"/berita/2.jpeg\",\n            date: \"19 Jul 2024\"\n        },\n        {\n            id: 3,\n            title: \"PJ Wali Kota Pekanbaru Tinjau Pelayanan MPP Pasca Beroperasi Hingga di DPMPTSP\",\n            image: \"/berita/3.jpeg\",\n            date: \"18 Jul 2024\"\n        },\n        {\n            id: 4,\n            title: \"Satgas Saber Pungli Provinsi Riau Sosialisasikan Aplikasi Si Dali ke DPMPTSP Kota Pekanbaru\",\n            image: \"/berita/4.jpg\",\n            date: \"10 Apr 2024\"\n        },\n        {\n            id: 5,\n            title: \"Ratusan Pengikba Rumah Makan Non Muslim Ajukan Izin Operasional selama Ramadan\",\n            image: \"/berita/5.jpg\",\n            date: \"26 Mar 2024\"\n        },\n        {\n            id: 6,\n            title: \"DPMPTSP Pekanbaru Telah Berikan Izin Operasi 94 RM Non Muslim\",\n            image: \"/berita/6.jpeg\",\n            date: \"20 Mar 2024\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold border-b-2 border-gray-200 pb-2 mb-4\",\n                children: \"LATEST NEWS POSTS\"\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-md rounded overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/berita/mainfeat.jpg\",\n                                    alt: \"News\",\n                                    className: \"w-full h-48 object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-md font-bold mb-2\",\n                                            children: \"2024 Investasi di Pekanbaru Serap Belasan Ribu Tenaga Kerja\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-red-600 text-white px-3 py-1 rounded text-sm\",\n                                            children: \"Read More\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: newsItems.slice(1).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex bg-white shadow-sm rounded overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 h-20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: item.image,\n                                                alt: item.title,\n                                                className: \"w-24 h-20 object-cover\",\n                                                style: {\n                                                    width: \"96px\",\n                                                    height: \"80px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: item.date\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\LatestNewsPosts.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LatestNewsPosts;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LatestNewsPosts);\nvar _c;\n$RefreshReg$(_c, \"LatestNewsPosts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/LatestNewsPosts.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/main/MainFeature.tsx":
/*!*********************************************!*\
  !*** ./src/components/main/MainFeature.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// src/components/MainFeature.tsx\n\n\nconst MainFeature = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: \"/berita/mainfeat.jpg\",\n                alt: \"Leader\",\n                className: \"w-full h-86 object-cover\"\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-full bg-black bg-opacity-60 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-white text-sm mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"17 Februari 2025\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"|\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Investasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-white text-xl font-bold\",\n                        children: \"2024 Investasi di Pekanbaru Serap Belasan Ribu Tenaga Kerja\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\MainFeature.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MainFeature;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainFeature);\nvar _c;\n$RefreshReg$(_c, \"MainFeature\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/MainFeature.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/main/PromoInvestasi.tsx":
/*!************************************************!*\
  !*** ./src/components/main/PromoInvestasi.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst PromoInvestasi = ()=>{\n    const investmentItems = [\n        {\n            id: 1,\n            image: \"/investasi/i1.jpg\",\n            title: \"Semester Pertama 2024, Investasi yang Masuk ke Pekanbaru Capai Rp2,978 Triliun\",\n            link: \"/investasi/semester-pertama-2024\"\n        },\n        {\n            id: 2,\n            image: \"/investasi/i2.jpg\",\n            title: \"Investasi PMDN dan PMA Pekanbaru di TW II Serap 4,074 Tenaga Kerja\",\n            link: \"/investasi/pmdn-pma-tw2\"\n        },\n        {\n            id: 3,\n            image: \"/investasi/i3.jpeg\",\n            title: \"Realisasi Investasi Pekanbaru Triwulan II Capai Rp 27,8 Triliun\",\n            link: \"/investasi/realisasi-tw2\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-bold border-b pb-2 mb-4\",\n                children: \"PROMOSI INVESTASI\"\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: investmentItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-md rounded overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: item.image,\n                                alt: item.title,\n                                className: \"w-full h-48 object-cover\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-sm text-center hover:text-green-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.link,\n                                        className: \"hover:underline\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\PromoInvestasi.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PromoInvestasi;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PromoInvestasi);\nvar _c;\n$RefreshReg$(_c, \"PromoInvestasi\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/PromoInvestasi.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/main/Sidebar.tsx":
/*!*****************************************!*\
  !*** ./src/components/main/Sidebar.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// src/components/Sidebar.tsx\n\n\nconst Sidebar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/images/banner_ombudsman.jpeg\",\n                    alt: \"Pelayanan Publik\",\n                    className: \"w-full mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-center font-bold mb-2\",\n                        children: \"VISI MISI PEKANBARU\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/visimisipku.jpg\",\n                        alt: \"Visi Misi\",\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-center font-bold mb-2\",\n                        children: \"MAKLUMAT PELAYANAN\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/MAKLUMATDPMPTSP.jpg\",\n                        alt: \"Maklumat Pelayanan\",\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-bold mb-2\",\n                        children: \"MPP KOTA PEKANBARU\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/mpp.jpg\",\n                        alt: \"MPP Logo\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\Sidebar.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/main/TopBanner.tsx":
/*!*******************************************!*\
  !*** ./src/components/main/TopBanner.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst TopBanner = ()=>{\n    _s();\n    // State to track the current banner index\n    const [currentBanner, setCurrentBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Sample banner data - you can replace with your actual data\n    const banners = [\n        {\n            alt: \"PROMOSI BISNIS\",\n            image: \"/Banner/B4.jpg\"\n        },\n        {\n            alt: \"POTENSI PARIWISATA\",\n            image: \"/Banner/B2.jpg\"\n        },\n        {\n            alt: \"PEMBANGUNAN DAERAH\",\n            image: \"/Banner/B3.jpg\"\n        }\n    ];\n    // Auto rotate banners every 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TopBanner.useEffect\": ()=>{\n            const interval = setInterval({\n                \"TopBanner.useEffect.interval\": ()=>{\n                    setCurrentBanner({\n                        \"TopBanner.useEffect.interval\": (prev)=>(prev + 1) % banners.length\n                    }[\"TopBanner.useEffect.interval\"]);\n                }\n            }[\"TopBanner.useEffect.interval\"], 5000);\n            // Clean up interval on component unmount\n            return ({\n                \"TopBanner.useEffect\": ()=>clearInterval(interval)\n            })[\"TopBanner.useEffect\"];\n        }\n    }[\"TopBanner.useEffect\"], [\n        banners.length\n    ]);\n    const banner = banners[currentBanner];\n    // Handler for manual banner navigation\n    const navigateTo = (index)=>{\n        setCurrentBanner(index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative p-4 mb-4 min-h-[160px] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 w-full h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: banners[currentBanner].image,\n                    alt: banners[currentBanner].alt,\n                    fill: true,\n                    className: \"object-cover\",\n                    priority: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\TopBanner.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\TopBanner.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 bottom-0 flex space-x-2 p-4 z-10\",\n                children: banners.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>navigateTo(index),\n                        className: \"w-3 h-3 rounded-full \".concat(index === currentBanner ? \"bg-white\" : \"bg-white/40\"),\n                        \"aria-label\": \"Navigate to banner \".concat(index + 1)\n                    }, index, false, {\n                        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\TopBanner.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\TopBanner.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\chatbot fe n be\\\\chatbot_ui\\\\src\\\\components\\\\main\\\\TopBanner.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TopBanner, \"U9PsEcjVbstOjbBgthqmFpKgY7U=\");\n_c = TopBanner;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TopBanner);\nvar _c;\n$RefreshReg$(_c, \"TopBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/TopBanner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxjaGF0Ym90IGZlIG4gYmVcXGNoYXRib3RfdWlcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchatbot%20fe%20n%20be%5C%5Cchatbot_ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);