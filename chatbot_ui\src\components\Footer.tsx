// src/components/Footer.tsx
const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white py-8">
      <div className="container mx-auto grid md:grid-cols-4 gap-8">
        <div>
          <h3 className="font-bold mb-4">DPMPTSP</h3>
          <p>
            <PERSON><PERSON> dan Pelayanan Terpadu Satu Pintu Kota
            Pekanbaru
          </p>
        </div>
        <div>
          <h3 className="font-bold mb-4">Jen<PERSON> Perizinan</h3>
          <ul>
            <li>Perizinan (22)</li>
            <li>Paralel (1)</li>
            <li><PERSON><PERSON><PERSON><PERSON> (31)</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-4">Recent Posts</h3>
          <ul>
            <li>Pembangunan Gedung Utama MPP Pekanbaru</li>
            <li>Kepala DPMPTSP Pekanbaru Ikuti Apel</li>
            <li>PJ Wali Kota Pekanbaru Tinjau Pelayanan</li>
          </ul>
        </div>
        <div>
          <h3 className="font-bold mb-4">Archive</h3>
          <ul>
            {[
              "Februari (7)",
              "Januari (3)",
              "Oktober (2)",
              "September (3)",
              "Agustus (11)",
              "Juli (4)",
            ].map((item) => (
              <li key={item}>{item}</li>
            ))}
          </ul>
        </div>
      </div>
      <div className="text-center mt-8 border-t border-gray-700 pt-4">
        © {currentYear} Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu
        Kota Pekanbaru
      </div>
    </footer>
  );
};

export default Footer;
