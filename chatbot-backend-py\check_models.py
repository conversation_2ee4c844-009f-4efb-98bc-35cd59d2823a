"""
System health checks and model validation
"""
import os
import asyncio
import google.generativeai as genai
from dotenv import load_dotenv
from database import async_engine, AsyncSessionLocal
from sqlalchemy.sql import text
import faiss
import numpy as np

load_dotenv()

async def check_database_connection() -> dict:
    """Check PostgreSQL database connection"""
    try:
        async with AsyncSessionLocal() as session:
            # Test basic query
            result = await session.execute(text("SELECT 1 as test"))
            test_result = result.fetchone()

            if test_result and test_result.test == 1:
                print("✅ Database connection: OK")
                return {
                    "status": "healthy",
                    "message": "Database connection successful",
                    "host": os.getenv("DB_HOST"),
                    "database": os.getenv("DB_NAME")
                }
            else:
                raise Exception("Database test query failed")

    except Exception as e:
        print(f"❌ Database connection: FAILED - {e}")
        return {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "host": os.getenv("DB_HOST"),
            "database": os.getenv("DB_NAME")
        }



async def check_gemini_api() -> dict:
    """Check Google Gemini API connection"""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise Exception("GEMINI_API_KEY not found in environment variables")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Test with a simple prompt
        model = genai.GenerativeModel(
            model_name=os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
        )
        
        test_response = await asyncio.to_thread(
            model.generate_content,
            "Say 'API test successful' in Indonesian",
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,
                max_output_tokens=50
            )
        )
        
        if test_response and test_response.text:
            print("✅ Gemini API: OK")
            return {
                "status": "healthy",
                "message": "Gemini API connection successful",
                "model": os.getenv("GEMINI_MODEL", "gemini-1.5-flash"),
                "test_response": test_response.text.strip()
            }
        else:
            raise Exception("Gemini API test response was empty")
            
    except Exception as e:
        print(f"❌ Gemini API: FAILED - {e}")
        return {
            "status": "unhealthy",
            "message": f"Gemini API connection failed: {str(e)}",
            "model": os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
        }

async def check_vector_db() -> dict:
    """Check FAISS vector database"""
    try:
        vector_db_path = os.getenv("VECTOR_DB_PATH", "./vector_db")
        faiss_index_path = os.getenv("FAISS_INDEX_PATH", "./vector_db/faiss_index.bin")
        
        # Ensure vector_db directory exists
        os.makedirs(vector_db_path, exist_ok=True)
        
        # Test FAISS functionality
        dimension = 768  # Standard embedding dimension
        test_vectors = np.random.random((10, dimension)).astype('float32')
        
        # Create a simple FAISS index for testing
        index = faiss.IndexFlatL2(dimension)
        index.add(test_vectors)
        
        # Test search
        query_vector = np.random.random((1, dimension)).astype('float32')
        _, indices = index.search(query_vector, k=3)
        
        if len(indices[0]) == 3:
            print("✅ Vector database (FAISS): OK")
            return {
                "status": "healthy",
                "message": "FAISS vector database working correctly",
                "vector_db_path": vector_db_path,
                "faiss_index_path": faiss_index_path,
                "dimension": dimension,
                "test_vectors_count": len(test_vectors)
            }
        else:
            raise Exception("FAISS search test failed")
            
    except Exception as e:
        print(f"❌ Vector database (FAISS): FAILED - {e}")
        return {
            "status": "unhealthy",
            "message": f"FAISS vector database failed: {str(e)}",
            "vector_db_path": os.getenv("VECTOR_DB_PATH", "./vector_db")
        }

async def check_file_system() -> dict:
    """Check file system permissions and directories"""
    try:
        # Check required directories
        required_dirs = [
            os.getenv("DOCS_UPLOAD_PATH", "./data/docs"),
            os.getenv("VECTOR_DB_PATH", "./vector_db")
        ]
        
        for dir_path in required_dirs:
            os.makedirs(dir_path, exist_ok=True)
            
            # Test write permission
            test_file = os.path.join(dir_path, "test_write.tmp")
            with open(test_file, "w") as f:
                f.write("test")
            
            # Test read permission
            with open(test_file, "r") as f:
                content = f.read()
            
            # Clean up
            os.remove(test_file)
            
            if content != "test":
                raise Exception(f"File system test failed for {dir_path}")
        
        print("✅ File system: OK")
        return {
            "status": "healthy",
            "message": "File system permissions OK",
            "directories": required_dirs
        }
        
    except Exception as e:
        print(f"❌ File system: FAILED - {e}")
        return {
            "status": "unhealthy",
            "message": f"File system check failed: {str(e)}"
        }

async def run_all_health_checks() -> dict:
    """Run all health checks and return comprehensive status"""
    print("🔍 Running system health checks...")
    
    checks = {
        "database": await check_database_connection(),
        "gemini_api": await check_gemini_api(),
        "vector_db": await check_vector_db(),
        "file_system": await check_file_system()
    }
    
    # Determine overall health
    all_healthy = all(check["status"] == "healthy" for check in checks.values())
    
    overall_status = {
        "overall_status": "healthy" if all_healthy else "unhealthy",
        "timestamp": asyncio.get_event_loop().time(),
        "checks": checks
    }
    
    if all_healthy:
        print("🎉 All health checks passed!")
    else:
        print("⚠️ Some health checks failed!")
        failed_checks = [name for name, check in checks.items() if check["status"] != "healthy"]
        print(f"Failed checks: {', '.join(failed_checks)}")
    
    return overall_status

if __name__ == "__main__":
    # Run health checks when script is executed directly
    async def main():
        results = await run_all_health_checks()
        print("\n" + "="*50)
        print("HEALTH CHECK SUMMARY")
        print("="*50)
        for check_name, result in results["checks"].items():
            status_icon = "✅" if result["status"] == "healthy" else "❌"
            print(f"{status_icon} {check_name.upper()}: {result['status']}")
            if result["status"] != "healthy":
                print(f"   Error: {result['message']}")
        print("="*50)
    
    asyncio.run(main())
