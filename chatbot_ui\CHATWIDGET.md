# ChatWidget - Komponen Chat Interaktif

ChatWidget adalah komponen React yang menyediakan antarmuka chat interaktif untuk layanan DPMPTSP (Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu). Komponen ini menggunakan Socket.IO untuk komunikasi real-time dengan backend, menyediakan pengalaman chat yang responsif dan dinamis.

## Fitur Utama

- 💬 **Antarmuka Chat Interaktif** - UI yang intuitif untuk percakapan dengan asisten virtual
- 🔄 **Komunikasi Real-time** - Menggunakan Socket.IO untuk komunikasi instan dengan backend
- 📱 **Responsif** - Tampilan yang dioptimalkan untuk desktop dan perangkat mobile
- 🔒 **Persetujuan Pengguna** - Meminta persetujuan pengguna untuk menyimpan data percakapan
- 💾 **Persistensi Data** - Menyimpan ID chat dan status persetujuan di localStorage
- ⚠️ **Penanganan Error** - Menampilkan pesan error dan opsi untuk mencoba kembali
- 🔄 **Indikator Status** - Menampilkan status koneksi dan indikator "sedang mengetik"

## Penggunaan

### 1. Impor Komponen

```jsx
import ChatWidget from './components/ChatWidget';
```

### 2. Tambahkan ke Aplikasi Anda

```jsx
function App() {
  return (
    <div className="app">
      {/* Konten aplikasi Anda */}
      <ChatWidget />
    </div>
  );
}
```

## Konfigurasi

Komponen ChatWidget menggunakan variabel lingkungan untuk konfigurasi:

```env
# .env.local
NEXT_PUBLIC_BACKEND_URL=http://your-backend-url.com
```

Jika tidak dikonfigurasi, akan menggunakan default `http://localhost:3001`.

## Komunikasi dengan Backend

ChatWidget berkomunikasi dengan backend menggunakan Socket.IO, **bukan HTTP request tradisional**. Berikut adalah event-event yang digunakan:

### Event yang Dikirim ke Server

| Event | Data | Deskripsi |
|-------|------|-----------|
| `join-chat` | `{ chatId }` | Bergabung dengan ruang chat berdasarkan ID |
| `check-terms-status` | `{ chatId }` | Memeriksa status persetujuan pengguna |
| `accept-terms` | `{ chatId }` | Menyetujui syarat penggunaan |
| `send-message` | `{ chatId, content, retryCount }` | Mengirim pesan ke server |
| `get-chat-history` | `{ chatId }` | Meminta riwayat chat |

### Event yang Diterima dari Server

| Event | Data | Deskripsi |
|-------|------|-----------|
| `terms-status` | `{ accepted: boolean }` | Status persetujuan pengguna |
| `chat-history` | `Message[]` | Riwayat percakapan |
| `new-messages` | `Message[]` | Pesan baru dari asisten |
| `error` | `{ message: string, code?: string }` | Pesan error |
| `chat-created` | `string` (newChatId) | ID chat baru |

## Struktur Data

### Tipe Message

```typescript
type Message = {
  id: string;
  content: string;
  timestamp: string;
  role: "user" | "assistant" | "system";
  status?: "error" | "sending" | "sent";
};
```

## Alur Komunikasi

1. **Inisialisasi**:
   ```typescript
   const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3001";
   socketRef.current = socketIO(BACKEND_URL);
   ```

2. **Mengirim Pesan**:
   ```typescript
   socketRef.current?.emit("send-message", {
     chatId,
     content: messageContent,
     retryCount: retryCount,
   });
   ```

3. **Menerima Pesan**:
   ```typescript
   socket.on("new-messages", (newMessages: Message[]) => {
     setIsBotTyping(false);
     setMessages((prevMessages) => {
       // Proses pesan baru
       // ...
     });
   });
   ```

## State Management

ChatWidget menggunakan beberapa state React untuk mengelola UI:

```typescript
// State utama
const [isOpen, setIsOpen] = useState(false);
const [showInitialTerms, setShowInitialTerms] = useState(true);
const [inputMessage, setInputMessage] = useState("");
const [isConnected, setIsConnected] = useState(false);
const [isBotTyping, setIsBotTyping] = useState(false);
const [messages, setMessages] = useState<Message[]>([]);
```

## Fitur Responsif

ChatWidget secara otomatis menyesuaikan tampilan berdasarkan ukuran layar:

```typescript
// Check for mobile screen size
useEffect(() => {
  const checkIsMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };
  
  // Check initially
  checkIsMobile();
  
  // Add resize listener
  window.addEventListener("resize", checkIsMobile);
  
  // Clean up
  return () => window.removeEventListener("resize", checkIsMobile);
}, []);
```

## Persistensi Data

ChatWidget menggunakan localStorage untuk menyimpan ID chat dan status persetujuan:

```typescript
// Menyimpan ID chat
if (typeof window !== "undefined") {
  localStorage.setItem("chat-widget-id", newChatId);
}

// Menyimpan status persetujuan
if (typeof window !== "undefined") {
  localStorage.setItem("chat-consent", "true");
}
```

## Penanganan Error

ChatWidget memiliki mekanisme penanganan error yang komprehensif:

```typescript
socket.on("error", (error: { message: string; code?: string }) => {
  // Handle API errors
  setIsBotTyping(false);

  // Set service error flag if it's a service availability issue
  if (error.code === "SERVICE_UNAVAILABLE") {
    setHasServiceError(true);
  }

  // Create a system message to display the error
  const errorMessage: Message = {
    id: uuidv4(),
    content:
      error.code === "SERVICE_UNAVAILABLE"
        ? "Maaf, layanan AI sedang sibuk. Mohon coba lagi setelah beberapa saat."
        : "Terjadi kesalahan dalam memproses pesan Anda. Mohon coba lagi nanti.",
    timestamp: new Date().toISOString(),
    role: "assistant",
    status: "error",
  };

  // Update messages
  setMessages((prev) => [...prev, errorMessage]);
});
```

## Kustomisasi

### Avatar

Ubah avatar asisten dengan mengganti konstanta `AVATAR_URL`:

```typescript
const AVATAR_URL = "/images/your-custom-avatar.png";
```

### Warna dan Tema

Komponen menggunakan Tailwind CSS untuk styling. Anda dapat menyesuaikan warna dan tema dengan mengubah kelas CSS di komponen.

## Persyaratan Backend

Backend harus mengimplementasikan event Socket.IO yang sesuai dengan yang digunakan oleh ChatWidget. Backend harus mampu:

1. Menerima dan memproses pesan dari pengguna
2. Mengirim respons asisten kembali ke client
3. Menyimpan dan mengembalikan riwayat chat
4. Mengelola status persetujuan pengguna
5. Menangani error dan mengirimkan pesan error yang sesuai

## Kesimpulan

ChatWidget adalah komponen chat yang lengkap dan siap pakai yang menggunakan Socket.IO untuk komunikasi real-time. Komponen ini menyediakan UI yang responsif dan intuitif untuk berinteraksi dengan asisten virtual, dengan fitur-fitur seperti persistensi data, penanganan error, dan indikator status.
