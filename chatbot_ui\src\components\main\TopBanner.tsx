import React, { useState, useEffect } from "react";
import Image from "next/image";

const TopBanner: React.FC = () => {
  // State to track the current banner index
  const [currentBanner, setCurrentBanner] = useState(0);

  // Sample banner data - you can replace with your actual data
  const banners = [
    {
      alt: "PROMOSI BISNIS",
      image: "/Banner/B4.jpg",
    },
    {
      alt: "POTENSI PARIWISATA",
      image: "/Banner/B2.jpg",
    },
    {
      alt: "PEMBANGUNAN DAERAH",
      image: "/Banner/B3.jpg",
    },
  ];

  // Auto rotate banners every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBanner((prev) => (prev + 1) % banners.length);
    }, 5000);

    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, [banners.length]);

  const banner = banners[currentBanner];

  // Handler for manual banner navigation
  const navigateTo = (index: number) => {
    setCurrentBanner(index);
  };

  return (
    <div className="w-full relative p-4 mb-4 min-h-[160px] overflow-hidden">
      {/* Image only - no text or overlay */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={banners[currentBanner].image}
          alt={banners[currentBanner].alt}
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Navigation dots */}
      <div className="absolute right-0 bottom-0 flex space-x-2 p-4 z-10">
        {banners.map((_, index) => (
          <button
            key={index}
            onClick={() => navigateTo(index)}
            className={`w-3 h-3 rounded-full ${
              index === currentBanner ? "bg-white" : "bg-white/40"
            }`}
            aria-label={`Navigate to banner ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default TopBanner;