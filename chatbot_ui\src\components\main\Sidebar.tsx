// src/components/Sidebar.tsx
import React from "react";

const Sidebar: React.FC = () => {
  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg p-4">
        <img
          src="/images/banner_ombudsman.jpeg"
          alt="Pelayanan Publik"
          className="w-full mb-4"
        />
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <h4 className="text-center font-bold mb-2">VISI MISI PEKANBARU</h4>
        <img src="/images/visimisipku.jpg" alt="Visi Misi" className="w-full" />
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <h4 className="text-center font-bold mb-2">MAKLUMAT PELAYANAN</h4>
        <img
          src="/images/MAKLUMATDPMPTSP.jpg"
          alt="Maklumat Pelayanan"
          className="w-full"
        />
      </div>

      <div className="bg-white p-4 rounded-lg shadow text-center">
        <h4 className="font-bold mb-2">MPP KOTA PEKANBARU</h4>
        <img
          src="/images/mpp.jpg"
          alt="MPP Logo"
        />
      </div>
    </div>
  );
};

export default Sidebar;
