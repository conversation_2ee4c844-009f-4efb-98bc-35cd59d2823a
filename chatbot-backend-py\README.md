# Chatbot Backend Python

A real-time AI chatbot backend built with FastAPI, PostgreSQL, and Google Gemini AI. Features include WebSocket-based chat, PDF document processing, vector search, and Indonesian language support.

## 🚀 Features

- **Real-time Chat**: WebSocket-based communication with typing indicators
- **AI Responses**: Google Gemini AI integration with Indonesian language support
- **PDF Processing**: Upload and process PDF documents for context-aware responses
- **Vector Search**: FAISS-based similarity search for document content
- **Database**: PostgreSQL with proper schema and indexing
- **Caching**: In-memory caching for improved performance
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Health Checks**: Comprehensive system health monitoring

## 📁 Project Structure

```
chatbot-backend-py/
├── main.py              # FastAPI application entry point
├── database.py          # Database models and connections
├── chat_service.py      # Core chat business logic
├── check_models.py      # System health checks
├── requirements.txt     # Python dependencies
├── .env                 # Environment variables
├── data/
│   └── docs/           # PDF document storage
├── vector_db/          # FAISS vector database
└── README.md
```

## 🛠️ Installation

1. **Clone and setup**:

```bash
cd chatbot-backend-py
```

2. **Install dependencies**:

```bash
pip install -r requirements.txt
```

3. **Configure environment variables**:
   Update `.env` file with your database and API credentials.

4. **Run the application**:

```bash
python main.py
```

## 🔧 Configuration

### Environment Variables

- `PORT`: Server port (default: 3001)
- `FRONTEND_URL`: Frontend URL for CORS
- `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`: PostgreSQL configuration
- `GEMINI_API_KEY`: Google Gemini API key

### Database Schema

The application uses PostgreSQL with the `chatbot` schema:

- `chatbot.chats`: Chat sessions with UUID primary keys
- `chatbot.messages`: Chat messages with foreign key references

## 📡 API Endpoints

### REST API

- `GET /`: Health check
- `GET /health`: Detailed system health check
- `POST /api/chat`: Create new chat session
- `GET /api/chat/{chat_id}/history`: Get chat history
- `POST /api/pdf/upload`: Upload PDF documents (to be implemented)

### WebSocket Events

**Client → Server:**

- `join-chat`: Join chat room and get history
- `send-message`: Send user message
- `typing-start/typing-end`: Typing indicators
- `reset-chat`: Reset/create new chat

**Server → Client:**

- `chat-history`: Send chat history
- `new-messages`: Send new messages
- `chat-created`: Send new chat ID
- `chat-reset`: Confirm chat reset
- `user-typing`: Typing indicator
- `error`: Error messages

## 🧪 Health Checks

Run system health checks:

```bash
python check_models.py
```

This checks:

- PostgreSQL database connection
- Redis connection
- Google Gemini API
- FAISS vector database
- File system permissions

## 🔄 Key Features

### Chat Management

- UUID-based chat sessions
- Message history with 10 message limit
- Content truncation (4096 characters max)
- Redis caching with 1-hour TTL

### AI Integration

- Google Gemini 1.5 Flash model
- Indonesian language system prompts
- Context-aware responses
- DPMPTSP (government office) specific responses

### Performance Optimizations

- Connection pooling (20 max connections)
- Redis caching for chat history
- Rate limiting (100 requests/15 minutes)
- Async/await throughout

### WebSocket Configuration

- CORS support for frontend
- Ping timeout: 60 seconds
- Ping interval: 25 seconds
- Room-based chat isolation

## 🚦 Getting Started

1. Ensure PostgreSQL is running
2. Update `.env` with your configuration
3. Install dependencies: `pip install -r requirements.txt`
4. Run health checks: `python check_models.py`
5. Start the server: `python main.py`

The server will be available at `http://localhost:3001` (or your configured PORT).

## 📝 Development

The codebase follows Python best practices with:

- Type hints throughout
- Async/await for I/O operations
- Proper error handling
- Comprehensive logging
- Modular architecture

## 🔐 Security

- Rate limiting on all endpoints
- Input validation and sanitization
- SQL injection protection via SQLAlchemy
- CORS configuration
- Environment variable protection
